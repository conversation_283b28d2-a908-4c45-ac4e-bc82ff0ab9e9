"use strict";
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ "(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js");
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js");
/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,TagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js");
/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Layout/Layout */ "(app-pages-browser)/./app/components/Layout/Layout.tsx");
/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../stores/authStore */ "(app-pages-browser)/./app/stores/authStore.ts");
/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../services/api */ "(app-pages-browser)/./app/services/api.ts");
/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();


var _jsxFileName = "C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\dashboard\\page.tsx", _this = undefined;
var __jsx = (react__WEBPACK_IMPORTED_MODULE_2___default().createElement);







var DashboardPage = function DashboardPage() {
    _s();
    var _useAuthStore = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore)(), user = _useAuthStore.user, isAuthenticated = _useAuthStore.isAuthenticated;
    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), stats = _useState[0], setStats = _useState[1];
    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), recentDocuments = _useState2[0], setRecentDocuments = _useState2[1];
    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), recentTasks = _useState3[0], setRecentTasks = _useState3[1];
    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true), loading = _useState4[0], setLoading = _useState4[1];
    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''), error = _useState5[0], setError = _useState5[1];
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({
        "DashboardPage.useEffect": function() {
            // Only fetch data if authenticated - Layout component handles auth redirects
            if (!isAuthenticated) {
                return;
            }
            var fetchDashboardData = /*#__PURE__*/ ({
                "DashboardPage.useEffect.fetchDashboardData": function() {
                    var _ref = (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/ C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
                        var statsResponse, documentsResponse, tasksResponse, _err$response;
                        return C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee$(_context) {
                            while(1)switch(_context.prev = _context.next){
                                case 0:
                                    _context.prev = 0;
                                    setLoading(true);
                                    // Fetch dashboard stats
                                    _context.next = 4;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_7__["default"].getDashboardStats();
                                case 4:
                                    statsResponse = _context.sent;
                                    setStats(statsResponse.data);
                                    // Fetch recent documents
                                    _context.next = 8;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_7__["default"].getDocuments({
                                        page: 1,
                                        per_page: 5,
                                        sort: 'created_at',
                                        order: 'desc'
                                    });
                                case 8:
                                    documentsResponse = _context.sent;
                                    setRecentDocuments(Array.isArray(documentsResponse.data) ? documentsResponse.data : []);
                                    // Fetch recent tasks
                                    _context.next = 12;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_7__["default"].getTasks({
                                        page: 1,
                                        per_page: 5,
                                        order_by: 'created_at'
                                    });
                                case 12:
                                    tasksResponse = _context.sent;
                                    setRecentTasks(Array.isArray(tasksResponse.data) ? tasksResponse.data : []);
                                    _context.next = 19;
                                    break;
                                case 16:
                                    _context.prev = 16;
                                    _context.t0 = _context["catch"](0);
                                    setError(((_err$response = _context.t0.response) === null || _err$response === void 0 || (_err$response = _err$response.data) === null || _err$response === void 0 ? void 0 : _err$response.message) || 'Failed to fetch dashboard data');
                                case 19:
                                    _context.prev = 19;
                                    setLoading(false);
                                    return _context.finish(19);
                                case 22:
                                case "end":
                                    return _context.stop();
                            }
                        }, _callee, null, [
                            [
                                0,
                                16,
                                19,
                                22
                            ]
                        ]);
                    }));
                    return function fetchDashboardData() {
                        return _ref.apply(this, arguments);
                    };
                }
            })["DashboardPage.useEffect.fetchDashboardData"]();
            fetchDashboardData();
        }
    }["DashboardPage.useEffect"], [
        isAuthenticated
    ]);
    return __jsx(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_5__["default"], {
        title: "Dashboard - Federal Register Clone",
        requireAuth: true,
        allowedRoles: [
            'viewer',
            'editor',
            'reviewer',
            'publisher',
            'admin'
        ],
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 89,
            columnNumber: 5
        }
    }, __jsx("div", {
        className: "container-custom py-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 94,
            columnNumber: 7
        }
    }, __jsx("div", {
        className: "mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 96,
            columnNumber: 9
        }
    }, __jsx("h1", {
        className: "text-3xl font-bold text-gray-900 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 97,
            columnNumber: 11
        }
    }, "Welcome back, ", (user === null || user === void 0 ? void 0 : user.first_name) || (user === null || user === void 0 ? void 0 : user.username), "!"), __jsx("p", {
        className: "text-gray-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 100,
            columnNumber: 11
        }
    }, "Here's what's happening with your documents and tasks today.")), error && __jsx("div", {
        className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 107,
            columnNumber: 11
        }
    }, error), loading ? __jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 113,
            columnNumber: 11
        }
    }, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(Array(4)).map(function(_, i) {
        return __jsx("div", {
            key: i,
            className: "bg-white rounded-lg shadow-md p-6 animate-pulse",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 115,
                columnNumber: 15
            }
        }, __jsx("div", {
            className: "h-8 bg-gray-200 rounded mb-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 116,
                columnNumber: 17
            }
        }), __jsx("div", {
            className: "h-6 bg-gray-200 rounded mb-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 117,
                columnNumber: 17
            }
        }), __jsx("div", {
            className: "h-4 bg-gray-200 rounded",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 118,
                columnNumber: 17
            }
        }));
    })) : __jsx((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), null, __jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 125,
            columnNumber: 13
        }
    }, __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 127,
            columnNumber: 15
        }
    }, __jsx("div", {
        className: "flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 128,
            columnNumber: 17
        }
    }, __jsx("div", {
        className: "flex-shrink-0",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 129,
            columnNumber: 19
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__["default"], {
        className: "h-8 w-8 text-blue-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 130,
            columnNumber: 21
        }
    })), __jsx("div", {
        className: "ml-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 132,
            columnNumber: 19
        }
    }, __jsx("h3", {
        className: "text-lg font-semibold text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 133,
            columnNumber: 21
        }
    }, "Documents"), __jsx("p", {
        className: "text-2xl font-bold text-blue-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 134,
            columnNumber: 21
        }
    }, (stats === null || stats === void 0 ? void 0 : stats.total_documents) || 0), __jsx("p", {
        className: "text-sm text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 135,
            columnNumber: 21
        }
    }, (stats === null || stats === void 0 ? void 0 : stats.published_documents) || 0, " published, ", (stats === null || stats === void 0 ? void 0 : stats.draft_documents) || 0, " draft")))), __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 143,
            columnNumber: 15
        }
    }, __jsx("div", {
        className: "flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 144,
            columnNumber: 17
        }
    }, __jsx("div", {
        className: "flex-shrink-0",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 145,
            columnNumber: 19
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__["default"], {
        className: "h-8 w-8 text-green-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 146,
            columnNumber: 21
        }
    })), __jsx("div", {
        className: "ml-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 148,
            columnNumber: 19
        }
    }, __jsx("h3", {
        className: "text-lg font-semibold text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 149,
            columnNumber: 21
        }
    }, "Tasks"), __jsx("p", {
        className: "text-2xl font-bold text-green-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 150,
            columnNumber: 21
        }
    }, (stats === null || stats === void 0 ? void 0 : stats.my_documents) || 0), __jsx("p", {
        className: "text-sm text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 151,
            columnNumber: 21
        }
    }, "My documents and tasks")))), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'editor') && __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 160,
            columnNumber: 17
        }
    }, __jsx("div", {
        className: "flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 161,
            columnNumber: 19
        }
    }, __jsx("div", {
        className: "flex-shrink-0",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 162,
            columnNumber: 21
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__["default"], {
        className: "h-8 w-8 text-purple-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 163,
            columnNumber: 23
        }
    })), __jsx("div", {
        className: "ml-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 165,
            columnNumber: 21
        }
    }, __jsx("h3", {
        className: "text-lg font-semibold text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 166,
            columnNumber: 23
        }
    }, "Agencies"), __jsx("p", {
        className: "text-2xl font-bold text-purple-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 167,
            columnNumber: 23
        }
    }, (stats === null || stats === void 0 ? void 0 : stats.total_agencies) || 0), __jsx("p", {
        className: "text-sm text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 168,
            columnNumber: 23
        }
    }, (stats === null || stats === void 0 ? void 0 : stats.total_agencies) || 0, " active")))), __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 177,
            columnNumber: 15
        }
    }, __jsx("div", {
        className: "flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 178,
            columnNumber: 17
        }
    }, __jsx("div", {
        className: "flex-shrink-0",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 179,
            columnNumber: 19
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__["default"], {
        className: "h-8 w-8 text-orange-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 180,
            columnNumber: 21
        }
    })), __jsx("div", {
        className: "ml-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 182,
            columnNumber: 19
        }
    }, __jsx("h3", {
        className: "text-lg font-semibold text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 183,
            columnNumber: 21
        }
    }, "Categories"), __jsx("p", {
        className: "text-2xl font-bold text-orange-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 184,
            columnNumber: 21
        }
    }, (stats === null || stats === void 0 ? void 0 : stats.total_categories) || 0), __jsx("p", {
        className: "text-sm text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 185,
            columnNumber: 21
        }
    }, "Available categories"))))), __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6 mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 192,
            columnNumber: 13
        }
    }, __jsx("h2", {
        className: "text-xl font-semibold text-gray-900 mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 193,
            columnNumber: 15
        }
    }, "Quick Actions"), __jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 194,
            columnNumber: 15
        }
    }, (user === null || user === void 0 ? void 0 : user.role) !== 'viewer' && __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/documents/new",
        className: "flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 196,
            columnNumber: 19
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__["default"], {
        className: "h-8 w-8 text-blue-600 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 200,
            columnNumber: 21
        }
    }), __jsx("span", {
        className: "text-sm font-medium text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 201,
            columnNumber: 21
        }
    }, "New Document")), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/tasks/new",
        className: "flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 205,
            columnNumber: 17
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__["default"], {
        className: "h-8 w-8 text-green-600 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 209,
            columnNumber: 19
        }
    }), __jsx("span", {
        className: "text-sm font-medium text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 210,
            columnNumber: 19
        }
    }, "New Task")), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/calendar",
        className: "flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 213,
            columnNumber: 17
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__["default"], {
        className: "h-8 w-8 text-purple-600 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 217,
            columnNumber: 19
        }
    }), __jsx("span", {
        className: "text-sm font-medium text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 218,
            columnNumber: 19
        }
    }, "Calendar")), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/search",
        className: "flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 221,
            columnNumber: 17
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__["default"], {
        className: "h-8 w-8 text-orange-600 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 225,
            columnNumber: 19
        }
    }), __jsx("span", {
        className: "text-sm font-medium text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 226,
            columnNumber: 19
        }
    }, "Search")), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/profile",
        className: "flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 229,
            columnNumber: 17
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__["default"], {
        className: "h-8 w-8 text-gray-600 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 233,
            columnNumber: 19
        }
    }), __jsx("span", {
        className: "text-sm font-medium text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 234,
            columnNumber: 19
        }
    }, "Profile")))), __jsx("div", {
        className: "grid grid-cols-1 lg:grid-cols-2 gap-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 240,
            columnNumber: 13
        }
    }, __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 242,
            columnNumber: 15
        }
    }, __jsx("div", {
        className: "flex items-center justify-between mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 243,
            columnNumber: 17
        }
    }, __jsx("h2", {
        className: "text-xl font-semibold text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 244,
            columnNumber: 19
        }
    }, "Recent Documents"), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/documents",
        className: "text-primary-600 hover:text-primary-700 text-sm font-medium",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 245,
            columnNumber: 19
        }
    }, "View all")), (recentDocuments || []).length === 0 ? __jsx("div", {
        className: "text-center py-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 254,
            columnNumber: 19
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__["default"], {
        className: "h-12 w-12 text-gray-400 mx-auto mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 255,
            columnNumber: 21
        }
    }), __jsx("p", {
        className: "text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 256,
            columnNumber: 21
        }
    }, "No recent documents")) : __jsx("div", {
        className: "space-y-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 259,
            columnNumber: 19
        }
    }, recentDocuments.map(function(document) {
        var _document$agency;
        return __jsx("div", {
            key: document.id,
            className: "flex items-center justify-between p-3 border border-gray-200 rounded-lg",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 261,
                columnNumber: 23
            }
        }, __jsx("div", {
            className: "flex-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 262,
                columnNumber: 25
            }
        }, __jsx("h4", {
            className: "font-medium text-gray-900 truncate",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 263,
                columnNumber: 27
            }
        }, __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
            href: "/documents/".concat(document.id),
            className: "hover:text-primary-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 264,
                columnNumber: 29
            }
        }, document.title)), __jsx("p", {
            className: "text-sm text-gray-500",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 271,
                columnNumber: 27
            }
        }, (_document$agency = document.agency) === null || _document$agency === void 0 ? void 0 : _document$agency.name, " \u2022 ", new Date(document.created_at).toLocaleDateString())), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
            href: "/documents/".concat(document.id),
            className: "p-2 text-gray-400 hover:text-primary-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 275,
                columnNumber: 25
            }
        }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__["default"], {
            className: "h-4 w-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 279,
                columnNumber: 27
            }
        })));
    }))), __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 288,
            columnNumber: 15
        }
    }, __jsx("div", {
        className: "flex items-center justify-between mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 289,
            columnNumber: 17
        }
    }, __jsx("h2", {
        className: "text-xl font-semibold text-gray-900",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 290,
            columnNumber: 19
        }
    }, "Recent Tasks"), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
        href: "/tasks",
        className: "text-primary-600 hover:text-primary-700 text-sm font-medium",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 291,
            columnNumber: 19
        }
    }, "View all")), (recentTasks || []).length === 0 ? __jsx("div", {
        className: "text-center py-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 300,
            columnNumber: 19
        }
    }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__["default"], {
        className: "h-12 w-12 text-gray-400 mx-auto mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 301,
            columnNumber: 21
        }
    }), __jsx("p", {
        className: "text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 302,
            columnNumber: 21
        }
    }, "No recent tasks")) : __jsx("div", {
        className: "space-y-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 305,
            columnNumber: 19
        }
    }, recentTasks.map(function(task) {
        var _task$status;
        return __jsx("div", {
            key: task.id,
            className: "flex items-center justify-between p-3 border border-gray-200 rounded-lg",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 307,
                columnNumber: 23
            }
        }, __jsx("div", {
            className: "flex-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 308,
                columnNumber: 25
            }
        }, __jsx("h4", {
            className: "font-medium text-gray-900 truncate",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 309,
                columnNumber: 27
            }
        }, __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
            href: "/tasks/".concat(task.id),
            className: "hover:text-primary-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 310,
                columnNumber: 29
            }
        }, task.title)), __jsx("div", {
            className: "flex items-center space-x-2 text-sm text-gray-500",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 317,
                columnNumber: 27
            }
        }, __jsx("span", {
            className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(task.status === 'completed' ? 'bg-green-100 text-green-800' : task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 318,
                columnNumber: 29
            }
        }, (_task$status = task.status) === null || _task$status === void 0 ? void 0 : _task$status.replace('_', ' ').toUpperCase()), task.due_date && __jsx("span", {
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 326,
                columnNumber: 31
            }
        }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__["default"], {
            className: "h-3 w-3 mr-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 327,
                columnNumber: 33
            }
        }), new Date(task.due_date).toLocaleDateString()))), __jsx((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {
            href: "/tasks/".concat(task.id),
            className: "p-2 text-gray-400 hover:text-primary-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 333,
                columnNumber: 25
            }
        }, __jsx(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_TagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__["default"], {
            className: "h-4 w-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 337,
                columnNumber: 27
            }
        })));
    })))))));
};
_s(DashboardPage, "aOdBhmNDDkICLU9KYJGgk7WsAH0=", false, function() {
    return [
        _stores_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore,
        _stores_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore,
        _stores_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore
    ];
});
_c = DashboardPage;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);
var _c;
$RefreshReg$(_c, "DashboardPage");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});