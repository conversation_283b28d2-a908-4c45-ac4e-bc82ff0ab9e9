"use strict";
self["webpackHotUpdate_N_E"]("app/documents/page",{

/***/ "(app-pages-browser)/./app/components/UI/DataTable.tsx":
/*!*****************************************!*\
  !*** ./app/components/UI/DataTable.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js");
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js");
/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js");
var _jsxFileName = "C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\components\\UI\\DataTable.tsx";
var __jsx = (react__WEBPACK_IMPORTED_MODULE_0___default().createElement);



function DataTable(_ref) {
    var _this = this;
    var data = _ref.data, columns = _ref.columns, _ref$actions = _ref.actions, actions = _ref$actions === void 0 ? [] : _ref$actions, _ref$loading = _ref.loading, loading = _ref$loading === void 0 ? false : _ref$loading, error = _ref.error, pagination = _ref.pagination, onPageChange = _ref.onPageChange, onSort = _ref.onSort, sortColumn = _ref.sortColumn, sortDirection = _ref.sortDirection, _ref$emptyMessage = _ref.emptyMessage, emptyMessage = _ref$emptyMessage === void 0 ? 'No data available' : _ref$emptyMessage, _ref$className = _ref.className, className = _ref$className === void 0 ? '' : _ref$className;
    var handleSort = function handleSort(column) {
        if (!column.sortable || !onSort) return;
        var columnKey = column.key;
        var newDirection = sortColumn === columnKey && sortDirection === 'asc' ? 'desc' : 'asc';
        onSort(columnKey, newDirection);
    };
    var getSortIcon = function getSortIcon(column) {
        if (!column.sortable) return null;
        var columnKey = column.key;
        if (sortColumn !== columnKey) {
            return __jsx("span", {
                className: "text-gray-400",
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 75,
                    columnNumber: 14
                }
            }, "\u2195");
        }
        return sortDirection === 'asc' ? __jsx("span", {
            className: "text-blue-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 79,
                columnNumber: 7
            }
        }, "\u2191") : __jsx("span", {
            className: "text-blue-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 80,
                columnNumber: 7
            }
        }, "\u2193");
    };
    var renderCell = function renderCell(item, column) {
        var keyStr = String(column.key);
        var value = keyStr.includes('.') ? keyStr.split('.').reduce(function(obj, key) {
            return obj === null || obj === void 0 ? void 0 : obj[key];
        }, item) : item[column.key];
        if (column.render) {
            return column.render(value, item);
        }
        return (value === null || value === void 0 ? void 0 : value.toString()) || '-';
    };
    var renderActions = function renderActions(item) {
        if (actions.length === 0) return null;
        return __jsx("td", {
            className: "px-6 py-4 whitespace-nowrap text-right text-sm font-medium",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 100,
                columnNumber: 7
            }
        }, __jsx("div", {
            className: "flex items-center justify-end space-x-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 101,
                columnNumber: 9
            }
        }, actions.map(function(action, index) {
            if (action.show && !action.show(item)) return null;
            // For href actions, ensure the item has a valid ID
            if (action.href && (!item || !item.id || item.id === undefined || item.id === null)) {
                return null;
            }
            var Icon = action.icon;
            var content = __jsx((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, Icon && __jsx(Icon, {
                className: "h-4 w-4",
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 113,
                    columnNumber: 26
                }
            }), action.label && __jsx("span", {
                className: "ml-1",
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 114,
                    columnNumber: 34
                }
            }, action.label));
            if (action.href) {
                var href = action.href(item);
                // Skip rendering if href contains undefined
                if (href.includes('undefined')) {
                    return null;
                }
                return __jsx((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {
                    key: index,
                    href: href,
                    className: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-600 hover:text-blue-900 ".concat(action.className || ''),
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 125,
                        columnNumber: 17
                    }
                }, content);
            }
            return __jsx("button", {
                key: index,
                onClick: function onClick() {
                    var _action$onClick;
                    return (_action$onClick = action.onClick) === null || _action$onClick === void 0 ? void 0 : _action$onClick.call(action, item);
                },
                className: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-600 hover:text-blue-900 ".concat(action.className || ''),
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 136,
                    columnNumber: 15
                }
            }, content);
        })));
    };
    var renderPagination = function renderPagination() {
        if (!pagination || !onPageChange) return null;
        var page = pagination.page, total_pages = pagination.total_pages;
        var pages = [];
        // Calculate page range
        var startPage = Math.max(1, page - 2);
        var endPage = Math.min(total_pages, page + 2);
        for(var i = startPage; i <= endPage; i++){
            pages.push(i);
        }
        return __jsx("div", {
            className: "bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 165,
                columnNumber: 7
            }
        }, __jsx("div", {
            className: "flex-1 flex justify-between sm:hidden",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 166,
                columnNumber: 9
            }
        }, __jsx("button", {
            onClick: function onClick() {
                return onPageChange(page - 1);
            },
            disabled: page <= 1,
            className: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 167,
                columnNumber: 11
            }
        }, "Previous"), __jsx("button", {
            onClick: function onClick() {
                return onPageChange(page + 1);
            },
            disabled: page >= total_pages,
            className: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 174,
                columnNumber: 11
            }
        }, "Next")), __jsx("div", {
            className: "hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 182,
                columnNumber: 9
            }
        }, __jsx("div", {
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 183,
                columnNumber: 11
            }
        }, __jsx("p", {
            className: "text-sm text-gray-700",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 184,
                columnNumber: 13
            }
        }, "Showing page ", __jsx("span", {
            className: "font-medium",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 185,
                columnNumber: 28
            }
        }, page), " of", ' ', __jsx("span", {
            className: "font-medium",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 186,
                columnNumber: 15
            }
        }, total_pages))), __jsx("div", {
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 189,
                columnNumber: 11
            }
        }, __jsx("nav", {
            className: "relative z-0 inline-flex rounded-md shadow-sm -space-x-px",
            "aria-label": "Pagination",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 190,
                columnNumber: 13
            }
        }, __jsx("button", {
            onClick: function onClick() {
                return onPageChange(page - 1);
            },
            disabled: page <= 1,
            className: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 191,
                columnNumber: 15
            }
        }, __jsx(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__["default"], {
            className: "h-5 w-5",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 196,
                columnNumber: 17
            }
        })), pages.map(function(pageNum) {
            return __jsx("button", {
                key: pageNum,
                onClick: function onClick() {
                    return onPageChange(pageNum);
                },
                className: "relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(pageNum === page ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'),
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 200,
                    columnNumber: 17
                }
            }, pageNum);
        }), __jsx("button", {
            onClick: function onClick() {
                return onPageChange(page + 1);
            },
            disabled: page >= total_pages,
            className: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 213,
                columnNumber: 15
            }
        }, __jsx(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__["default"], {
            className: "h-5 w-5",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 218,
                columnNumber: 17
            }
        }))))));
    };
    if (loading) {
        return __jsx("div", {
            className: "bg-white shadow overflow-hidden sm:rounded-md ".concat(className),
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 229,
                columnNumber: 7
            }
        }, __jsx("div", {
            className: "px-4 py-8 text-center",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 230,
                columnNumber: 9
            }
        }, __jsx("div", {
            className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 231,
                columnNumber: 11
            }
        }), __jsx("p", {
            className: "mt-2 text-sm text-gray-500",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 232,
                columnNumber: 11
            }
        }, "Loading...")));
    }
    if (error) {
        return __jsx("div", {
            className: "bg-white shadow overflow-hidden sm:rounded-md ".concat(className),
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 240,
                columnNumber: 7
            }
        }, __jsx("div", {
            className: "px-4 py-8 text-center",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 241,
                columnNumber: 9
            }
        }, __jsx("p", {
            className: "text-sm text-red-600",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 242,
                columnNumber: 11
            }
        }, error)));
    }
    if (data.length === 0) {
        return __jsx("div", {
            className: "bg-white shadow overflow-hidden sm:rounded-md ".concat(className),
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 250,
                columnNumber: 7
            }
        }, __jsx("div", {
            className: "px-4 py-8 text-center",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 251,
                columnNumber: 9
            }
        }, __jsx("p", {
            className: "text-sm text-gray-500",
            __self: this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 252,
                columnNumber: 11
            }
        }, emptyMessage)));
    }
    return __jsx("div", {
        className: "bg-white shadow overflow-hidden sm:rounded-md ".concat(className),
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 259,
            columnNumber: 5
        }
    }, __jsx("div", {
        className: "overflow-x-auto",
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 260,
            columnNumber: 7
        }
    }, __jsx("table", {
        className: "min-w-full divide-y divide-gray-200",
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 261,
            columnNumber: 9
        }
    }, __jsx("thead", {
        className: "bg-gray-50",
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 262,
            columnNumber: 11
        }
    }, __jsx("tr", {
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 263,
            columnNumber: 13
        }
    }, columns.map(function(column, index) {
        return __jsx("th", {
            key: index,
            className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ".concat(column.sortable ? 'cursor-pointer hover:bg-gray-100' : '', " ").concat(column.className || ''),
            onClick: function onClick() {
                return handleSort(column);
            },
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 265,
                columnNumber: 17
            }
        }, __jsx("div", {
            className: "flex items-center space-x-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 272,
                columnNumber: 19
            }
        }, __jsx("span", {
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 273,
                columnNumber: 21
            }
        }, column.label), getSortIcon(column)));
    }), actions.length > 0 && __jsx("th", {
        className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 279,
            columnNumber: 17
        }
    }, "Actions"))), __jsx("tbody", {
        className: "bg-white divide-y divide-gray-200",
        __self: this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 285,
            columnNumber: 11
        }
    }, data.map(function(item, index) {
        return __jsx("tr", {
            key: item.id,
            className: "hover:bg-gray-50",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 287,
                columnNumber: 15
            }
        }, columns.map(function(column, colIndex) {
            return __jsx("td", {
                key: colIndex,
                className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900 ".concat(column.className || ''),
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 289,
                    columnNumber: 19
                }
            }, renderCell(item, column));
        }), renderActions(item));
    })))), renderPagination());
}
_c = DataTable;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataTable);
var _c;
$RefreshReg$(_c, "DataTable");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});