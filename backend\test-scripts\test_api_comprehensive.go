package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

const baseURL = "http://127.0.0.1:8080/api/v1"

// generateUniqueID generates a unique identifier using timestamp
func generateUniqueID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

type TestResult struct {
	Entity     string
	Operation  string
	Success    bool
	Error      string
	StatusCode int
	Response   string
	Endpoint   string
	Timestamp  time.Time
	ErrorType  string // "HTTP_ERROR", "SQL_ERROR", "TABLE_ERROR", "AUTH_ERROR", "VALIDATION_ERROR"
}

type ErrorLog struct {
	Timestamp  time.Time
	Entity     string
	Operation  string
	Endpoint   string
	StatusCode int
	ErrorType  string
	ErrorMsg   string
	Response   string
	IsCritical bool
}

var errorLogs []ErrorLog
var logFile *os.File

type AuthResponse struct {
	Token string `json:"token"`
	User  struct {
		ID   uint   `json:"id"`
		Role string `json:"role"`
	} `json:"user"`
}

var authToken string
var userID uint

// initializeLogging sets up the error logging system
func initializeLogging() {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("api_test_errors_%s.log", timestamp)

	var err error
	logFile, err = os.Create(filename)
	if err != nil {
		fmt.Printf("⚠️ Warning: Could not create log file: %v\n", err)
		return
	}

	fmt.Printf("📝 Error logging initialized: %s\n", filename)
	writeLogFile("=== API COMPREHENSIVE TEST ERROR LOG ===")
	writeLogFile(fmt.Sprintf("Test started at: %s", time.Now().Format("2006-01-02 15:04:05")))
	writeLogFile("=" + strings.Repeat("=", 50))
}

// closeLogging closes the log file
func closeLogging() {
	if logFile != nil {
		writeLogFile("=" + strings.Repeat("=", 50))
		writeLogFile(fmt.Sprintf("Test completed at: %s", time.Now().Format("2006-01-02 15:04:05")))
		writeLogFile("=== END OF LOG ===")
		logFile.Close()
	}
}

// writeLogFile writes a message to the log file
func writeLogFile(message string) {
	if logFile != nil {
		logFile.WriteString(fmt.Sprintf("%s\n", message))
		logFile.Sync() // Ensure immediate write
	}
}

// logError logs a test result error with detailed analysis
func logError(result TestResult) {
	if result.Success {
		return // Only log failures
	}

	errorType := categorizeError(result)
	isCritical := isCriticalError(result)

	errorLog := ErrorLog{
		Timestamp:  time.Now(),
		Entity:     result.Entity,
		Operation:  result.Operation,
		Endpoint:   result.Endpoint,
		StatusCode: result.StatusCode,
		ErrorType:  errorType,
		ErrorMsg:   result.Error,
		Response:   result.Response,
		IsCritical: isCritical,
	}

	errorLogs = append(errorLogs, errorLog)

	// Write to log file immediately
	logMessage := fmt.Sprintf("[%s] %s | %s %s | Status: %d | Type: %s | Critical: %t",
		errorLog.Timestamp.Format("15:04:05"),
		errorLog.Entity,
		errorLog.Operation,
		errorLog.Endpoint,
		errorLog.StatusCode,
		errorLog.ErrorType,
		errorLog.IsCritical)

	writeLogFile(logMessage)
	writeLogFile(fmt.Sprintf("  Error: %s", errorLog.ErrorMsg))

	if len(errorLog.Response) > 0 && len(errorLog.Response) < 500 {
		writeLogFile(fmt.Sprintf("  Response: %s", errorLog.Response))
	}
	writeLogFile("") // Empty line for readability
}

// categorizeError determines the type of error based on status code and response
func categorizeError(result TestResult) string {
	// Check status codes first
	switch result.StatusCode {
	case 401:
		return "AUTH_ERROR"
	case 404:
		return "NOT_FOUND_ERROR"
	case 500:
		return "SERVER_ERROR"
	case 400:
		return "VALIDATION_ERROR"
	case 403:
		return "PERMISSION_ERROR"
	}

	// Check response content for SQL/database errors
	response := strings.ToLower(result.Response)
	error := strings.ToLower(result.Error)

	sqlKeywords := []string{
		"table", "column", "index", "constraint", "foreign key",
		"sql", "database", "relation", "does not exist",
		"syntax error", "duplicate key", "violates",
	}

	for _, keyword := range sqlKeywords {
		if strings.Contains(response, keyword) || strings.Contains(error, keyword) {
			return "SQL_ERROR"
		}
	}

	// Check for table/schema errors
	tableKeywords := []string{
		"table", "column", "does not exist", "unknown column",
		"no such table", "relation", "schema",
	}

	for _, keyword := range tableKeywords {
		if strings.Contains(response, keyword) || strings.Contains(error, keyword) {
			return "TABLE_ERROR"
		}
	}

	// Default to HTTP error
	if result.StatusCode > 0 {
		return "HTTP_ERROR"
	}

	return "UNKNOWN_ERROR"
}

// isCriticalError determines if an error is critical and needs immediate attention
func isCriticalError(result TestResult) bool {
	// 500 errors are always critical
	if result.StatusCode == 500 {
		return true
	}

	// SQL/Table errors are critical
	errorType := categorizeError(result)
	if errorType == "SQL_ERROR" || errorType == "TABLE_ERROR" {
		return true
	}

	// Auth errors on basic endpoints are critical
	if result.StatusCode == 401 && (result.Entity == "auth" || result.Entity == "health") {
		return true
	}

	return false
}

func main() {
	fmt.Println("🚀 Starting Comprehensive API Testing System with Enhanced Error Logging")
	fmt.Println(strings.Repeat("=", 80))

	// Initialize logging
	initializeLogging()
	defer closeLogging()

	// Step 1: Test health check first
	fmt.Println("🏥 Testing health check...")
	healthResult := testHealthCheck()
	if !healthResult.Success {
		fmt.Printf("❌ Health check failed: %s\n", healthResult.Error)
		fmt.Println("⚠️  Backend may not be running properly")
		logError(healthResult)
	}

	// Step 2: Authenticate
	if !authenticate() {
		fmt.Println("❌ Authentication failed. Exiting.")
		writeLogFile("CRITICAL: Authentication failed - cannot proceed with testing")
		os.Exit(1)
	}

	var results []TestResult
	results = append(results, healthResult)

	// Step 3: Test all entities systematically
	entities := []string{
		"auth", "preloading", "agencies", "categories", "documents",
		"regulations", "tasks", "proceedings", "finance", "finance/categories",
		"users", "summaries", "calendar", "search", "tags", "subjects",
		"comments", "analytics", "roles", "signatures", "certificates",
		"retention-policies", "processing", "enterprise", "public",
		"document-files", "document-analysis", "interconnect", "auto-generation",
		"regulation-chunks", "regulation-relationships", "task-performance",
	}

	for _, entity := range entities {
		fmt.Printf("\n📋 Testing %s...\n", entity)
		entityResults := testEntity(entity)
		results = append(results, entityResults...)

		// Log all errors for this entity
		for _, result := range entityResults {
			if !result.Success {
				logError(result)
			}
		}
	}

	// Step 4: Test specific endpoint combinations
	fmt.Printf("\n🔗 Testing endpoint combinations...\n")
	combinationResults := testEndpointCombinations()
	results = append(results, combinationResults...)

	// Log combination errors
	for _, result := range combinationResults {
		if !result.Success {
			logError(result)
		}
	}

	// Step 5: Generate comprehensive report
	generateReport(results)

	// Step 6: Generate detailed error analysis
	generateErrorAnalysis()
}

// generateErrorAnalysis creates a detailed analysis of all errors found
func generateErrorAnalysis() {
	if len(errorLogs) == 0 {
		fmt.Println("\n🎉 No errors found! All API endpoints are working correctly.")
		writeLogFile("SUCCESS: No errors found during testing!")
		return
	}

	fmt.Printf("\n" + strings.Repeat("=", 80) + "\n")
	fmt.Printf("🔍 DETAILED ERROR ANALYSIS - %d ERRORS FOUND\n", len(errorLogs))
	fmt.Printf(strings.Repeat("=", 80) + "\n")

	writeLogFile(fmt.Sprintf("\n=== DETAILED ERROR ANALYSIS - %d ERRORS FOUND ===", len(errorLogs)))

	// Group errors by type
	errorsByType := make(map[string][]ErrorLog)
	criticalErrors := 0

	for _, errorLog := range errorLogs {
		errorsByType[errorLog.ErrorType] = append(errorsByType[errorLog.ErrorType], errorLog)
		if errorLog.IsCritical {
			criticalErrors++
		}
	}

	// Display critical errors first
	if criticalErrors > 0 {
		fmt.Printf("\n🚨 CRITICAL ERRORS (%d) - REQUIRE IMMEDIATE ATTENTION:\n", criticalErrors)
		writeLogFile(fmt.Sprintf("\nCRITICAL ERRORS (%d):", criticalErrors))

		for _, errorLog := range errorLogs {
			if errorLog.IsCritical {
				fmt.Printf("  ❌ %s %s %s (Status: %d)\n",
					errorLog.Entity, errorLog.Operation, errorLog.Endpoint, errorLog.StatusCode)
				fmt.Printf("     Error: %s\n", errorLog.ErrorMsg)

				writeLogFile(fmt.Sprintf("  - %s %s %s (Status: %d): %s",
					errorLog.Entity, errorLog.Operation, errorLog.Endpoint, errorLog.StatusCode, errorLog.ErrorMsg))
			}
		}
	}

	// Display errors by type
	fmt.Printf("\n📊 ERRORS BY TYPE:\n")
	writeLogFile("\nERRORS BY TYPE:")

	for errorType, errors := range errorsByType {
		fmt.Printf("\n🔸 %s (%d errors):\n", errorType, len(errors))
		writeLogFile(fmt.Sprintf("\n%s (%d errors):", errorType, len(errors)))

		for _, errorLog := range errors {
			fmt.Printf("  • %s %s %s (Status: %d)\n",
				errorLog.Entity, errorLog.Operation, errorLog.Endpoint, errorLog.StatusCode)
			writeLogFile(fmt.Sprintf("  - %s %s %s (Status: %d): %s",
				errorLog.Entity, errorLog.Operation, errorLog.Endpoint, errorLog.StatusCode, errorLog.ErrorMsg))
		}
	}

	// Generate fix recommendations
	generateFixRecommendations(errorsByType)

	fmt.Printf("\n📝 Full error log saved to file for detailed analysis.\n")
	fmt.Printf("🔧 Review the recommendations above to fix all issues.\n")
	fmt.Printf(strings.Repeat("=", 80) + "\n")
}

// generateFixRecommendations provides specific recommendations for fixing errors
func generateFixRecommendations(errorsByType map[string][]ErrorLog) {
	fmt.Printf("\n🔧 FIX RECOMMENDATIONS:\n")
	writeLogFile("\nFIX RECOMMENDATIONS:")

	for errorType, errors := range errorsByType {
		switch errorType {
		case "SQL_ERROR", "TABLE_ERROR":
			fmt.Printf("\n🗃️  DATABASE ISSUES (%s):\n", errorType)
			fmt.Printf("   • Run database migrations to create missing tables/columns\n")
			fmt.Printf("   • Check database schema matches model definitions\n")
			fmt.Printf("   • Verify foreign key constraints are properly defined\n")

			writeLogFile(fmt.Sprintf("\nDATABASE ISSUES (%s):", errorType))
			writeLogFile("  - Run database migrations")
			writeLogFile("  - Check schema matches models")
			writeLogFile("  - Verify foreign key constraints")

		case "AUTH_ERROR":
			fmt.Printf("\n🔐 AUTHENTICATION ISSUES:\n")
			fmt.Printf("   • Check JWT token generation and validation\n")
			fmt.Printf("   • Verify middleware authentication logic\n")
			fmt.Printf("   • Ensure proper token headers are being sent\n")

			writeLogFile("\nAUTHENTICATION ISSUES:")
			writeLogFile("  - Check JWT token generation/validation")
			writeLogFile("  - Verify middleware authentication")
			writeLogFile("  - Ensure proper token headers")

		case "NOT_FOUND_ERROR":
			fmt.Printf("\n🔍 MISSING ENDPOINTS:\n")
			fmt.Printf("   • Implement missing API handlers in router.go\n")
			fmt.Printf("   • Add missing routes to the routing table\n")
			fmt.Printf("   • Check URL patterns and parameter handling\n")

			writeLogFile("\nMISSING ENDPOINTS:")
			writeLogFile("  - Implement missing API handlers")
			writeLogFile("  - Add missing routes to router.go")
			writeLogFile("  - Check URL patterns")

		case "SERVER_ERROR":
			fmt.Printf("\n💥 SERVER ERRORS:\n")
			fmt.Printf("   • Check handler implementation for nil pointer dereferences\n")
			fmt.Printf("   • Verify database connection and query syntax\n")
			fmt.Printf("   • Add proper error handling and validation\n")

			writeLogFile("\nSERVER ERRORS:")
			writeLogFile("  - Check handler implementations")
			writeLogFile("  - Verify database connections")
			writeLogFile("  - Add proper error handling")

		case "VALIDATION_ERROR":
			fmt.Printf("\n✅ VALIDATION ISSUES:\n")
			fmt.Printf("   • Check request body validation rules\n")
			fmt.Printf("   • Verify required fields are properly defined\n")
			fmt.Printf("   • Update model validation tags\n")

			writeLogFile("\nVALIDATION ISSUES:")
			writeLogFile("  - Check request validation rules")
			writeLogFile("  - Verify required fields")
			writeLogFile("  - Update model validation tags")
		}

		// List specific endpoints that need attention
		if len(errors) > 0 {
			fmt.Printf("   Affected endpoints:\n")
			writeLogFile("   Affected endpoints:")
			for _, errorLog := range errors {
				fmt.Printf("   • %s %s\n", errorLog.Operation, errorLog.Endpoint)
				writeLogFile(fmt.Sprintf("   - %s %s", errorLog.Operation, errorLog.Endpoint))
			}
		}
	}
}

func authenticate() bool {
	fmt.Println("🔐 Authenticating...")

	loginData := map[string]string{
		"identifier": "<EMAIL>",
		"password":   "testpass123",
	}

	resp, err := makeRequest("POST", "/auth/login", loginData, "")
	if err != nil {
		fmt.Printf("❌ Login request failed: %v\n", err)
		return false
	}

	var authResp AuthResponse
	if err := json.Unmarshal([]byte(resp), &authResp); err != nil {
		fmt.Printf("❌ Failed to parse auth response: %v\n", err)
		return false
	}

	authToken = authResp.Token
	userID = authResp.User.ID

	if authToken == "" {
		fmt.Printf("❌ Authentication failed: empty token received\n")
		fmt.Printf("Response: %s\n", resp)
		return false
	}

	fmt.Printf("✅ Authenticated as user ID %d with role %s\n", userID, authResp.User.Role)
	if len(authToken) >= 20 {
		fmt.Printf("🔑 Token: %s...\n", authToken[:20]) // Show first 20 chars of token for debugging
	} else {
		fmt.Printf("🔑 Token: %s\n", authToken) // Show full token if short
	}
	return true
}

// testHealthCheck tests the health endpoint
func testHealthCheck() TestResult {
	// Health endpoint is at root level, not under /api/v1
	healthURL := "http://127.0.0.1:8080/health"

	req, err := http.NewRequest("GET", healthURL, nil)
	if err != nil {
		return TestResult{
			Entity:     "health",
			Operation:  "GET /health",
			Success:    false,
			Error:      err.Error(),
			StatusCode: 0,
			Response:   "",
		}
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return TestResult{
			Entity:     "health",
			Operation:  "GET /health",
			Success:    false,
			Error:      err.Error(),
			StatusCode: 0,
			Response:   "",
		}
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			Entity:     "health",
			Operation:  "GET /health",
			Success:    false,
			Error:      err.Error(),
			StatusCode: resp.StatusCode,
			Response:   "",
		}
	}

	responseStr := string(responseBody)

	// Parse response to check if it's valid JSON
	var healthResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &healthResp); err != nil {
		return TestResult{
			Entity:     "health",
			Operation:  "GET /health",
			Success:    false,
			Error:      "Invalid JSON response: " + err.Error(),
			StatusCode: resp.StatusCode,
			Response:   responseStr,
		}
	}

	return TestResult{
		Entity:     "health",
		Operation:  "GET /health",
		Success:    true,
		Error:      "",
		StatusCode: resp.StatusCode,
		Response:   responseStr,
	}
}

func testEntity(entity string) []TestResult {
	var results []TestResult

	switch entity {
	case "auth":
		results = append(results, testAuthEndpoints()...)
	case "preloading":
		results = append(results, testPreloadingEndpoints()...)
	case "agencies":
		results = append(results, testAgencies()...)
	case "categories":
		results = append(results, testCategories()...)
	case "documents":
		results = append(results, testDocuments()...)
	case "regulations":
		results = append(results, testRegulations()...)
	case "tasks":
		results = append(results, testTasks()...)
	case "proceedings":
		results = append(results, testProceedings()...)
	case "finance":
		results = append(results, testFinance()...)
	case "finance/categories":
		results = append(results, testFinanceCategories()...)
	case "users":
		results = append(results, testUsers()...)
	case "summaries":
		results = append(results, testSummaries()...)
	case "calendar":
		results = append(results, testCalendar()...)
	case "search":
		results = append(results, testSearch()...)
	case "tags":
		results = append(results, testTags()...)
	case "subjects":
		results = append(results, testSubjects()...)
	case "comments":
		results = append(results, testComments()...)
	case "analytics":
		results = append(results, testAnalytics()...)
	case "roles":
		results = append(results, testRoles()...)
	case "signatures":
		results = append(results, testSignatures()...)
	case "certificates":
		results = append(results, testCertificates()...)
	case "retention-policies":
		results = append(results, testRetentionPolicies()...)
	case "processing":
		results = append(results, testProcessing()...)
	case "enterprise":
		results = append(results, testEnterpriseEndpoints()...)
	case "public":
		results = append(results, testPublicEndpoints()...)
	case "document-files":
		results = append(results, testDocumentFiles()...)
	case "document-analysis":
		results = append(results, testDocumentAnalysis()...)
	case "interconnect":
		results = append(results, testInterconnect()...)
	case "auto-generation":
		results = append(results, testAutoGeneration()...)
	case "regulation-chunks":
		results = append(results, testRegulationChunks()...)
	case "regulation-relationships":
		results = append(results, testRegulationRelationships()...)
	case "task-performance":
		results = append(results, testTaskPerformance()...)
	}

	return results
}

// testEndpointCombinations tests complex endpoint combinations
func testEndpointCombinations() []TestResult {
	var results []TestResult

	// Test document-category relationships
	results = append(results, testGetRequest("combinations", "/documents?category_id=1"))
	results = append(results, testGetRequest("combinations", "/categories/1/documents"))

	// Test agency-document relationships
	results = append(results, testGetRequest("combinations", "/agencies/1/documents"))
	results = append(results, testGetRequest("combinations", "/documents?agency_id=1"))

	// Test search combinations
	results = append(results, testGetRequest("combinations", "/search?q=test&type=documents"))
	results = append(results, testGetRequest("combinations", "/search?q=test&type=regulations"))

	// Test pagination combinations
	results = append(results, testGetRequest("combinations", "/documents?page=1&per_page=10"))
	results = append(results, testGetRequest("combinations", "/agencies?page=1&per_page=5"))

	return results
}

// testAuthEndpoints tests all authentication endpoints
func testAuthEndpoints() []TestResult {
	var results []TestResult

	// Test refresh token
	if authToken != "" {
		refreshData := map[string]string{
			"refresh_token": authToken, // Using access token as refresh for testing
		}
		results = append(results, testPostRequest("auth", "/auth/refresh", refreshData))
	}

	// Test logout
	results = append(results, testPostRequest("auth", "/auth/logout", map[string]string{}))

	// Test forgot password
	forgotData := map[string]string{
		"email": "<EMAIL>",
	}
	results = append(results, testPostRequest("auth", "/auth/forgot-password", forgotData))

	// Test reset password with a test token (this will likely fail but we want to test the endpoint)
	resetData := map[string]string{
		"token":            "test-reset-token-12345",
		"password":         "newpassword123",
		"confirm_password": "newpassword123",
	}
	results = append(results, testPostRequest("auth", "/auth/reset-password", resetData))

	// Test change password (for authenticated users)
	changePasswordData := map[string]string{
		"current_password": "password",
		"new_password":     "newpassword123",
		"confirm_password": "newpassword123",
	}
	results = append(results, testPostRequest("auth", "/auth/change-password", changePasswordData))

	return results
}

// testPreloadingEndpoints tests all preloading endpoints
func testPreloadingEndpoints() []TestResult {
	var results []TestResult

	// Test all preloading endpoints
	preloadingEndpoints := []string{
		"/preloading/documents",
		"/preloading/regulations",
		"/preloading/agencies",
		"/preloading/categories",
		"/preloading/proceedings",
		"/preloading/finances",
		"/preloading/slug?text=test-document-title",
		"/preloading/fr-number",
		"/preloading/docket-number?agency_id=1",
	}

	for _, endpoint := range preloadingEndpoints {
		results = append(results, testGetRequest("preloading", endpoint))
	}

	return results
}

func testAgencies() []TestResult {
	var results []TestResult

	// Test GET all agencies
	results = append(results, testGetRequest("agencies", "/agencies"))

	// Test CREATE agency

	uniqueID := generateUniqueID()
	agencyData := map[string]interface{}{
		"name":         "Test Environmental Protection Agency " + uniqueID,
		"short_name":   "TEPA" + uniqueID[:8],
		"slug":         "test-environmental-protection-agency-" + uniqueID,
		"description":  "Test agency for environmental protection",
		"website":      "https://test-epa.gov",
		"email":        "<EMAIL>",
		"phone":        "******-0123",
		"agency_type":  "federal",
		"jurisdiction": "national",
		"is_active":    true,
	}

	createResult := testCreateRequest("agencies", "/agencies", agencyData)
	results = append(results, createResult)

	if createResult.Success {
		// Extract ID from response
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				// Test GET single agency
				results = append(results, testGetRequest("agencies", fmt.Sprintf("/agencies/%d", id)))

				// Test UPDATE agency
				updateData := map[string]interface{}{
					"description": "Updated test agency description",
					"phone":       "******-0124",
				}
				results = append(results, testUpdateRequest("agencies", fmt.Sprintf("/agencies/%d", id), updateData))

				// Test DELETE agency
				results = append(results, testDeleteRequest("agencies", fmt.Sprintf("/agencies/%d", id)))
			}
		}
	}

	return results
}

func testCategories() []TestResult {
	var results []TestResult

	// Test GET all categories
	results = append(results, testGetRequest("categories", "/categories"))

	// Test CREATE category
	uniqueID := generateUniqueID()
	categoryData := map[string]interface{}{
		"name":        "Test Environmental Regulations " + uniqueID,
		"slug":        "test-environmental-regulations-" + uniqueID,
		"description": "Test category for environmental regulations",
		"color":       "#28a745",
		"cfr_title":   "40",
		"is_active":   true,
	}

	createResult := testCreateRequest("categories", "/categories", categoryData)
	results = append(results, createResult)

	if createResult.Success {
		// Extract ID and test other operations
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				results = append(results, testGetRequest("categories", fmt.Sprintf("/categories/%d", id)))

				updateData := map[string]interface{}{
					"description": "Updated test category description",
				}
				results = append(results, testUpdateRequest("categories", fmt.Sprintf("/categories/%d", id), updateData))
				results = append(results, testDeleteRequest("categories", fmt.Sprintf("/categories/%d", id)))
			}
		}
	}

	return results
}

func testDocuments() []TestResult {
	var results []TestResult

	// Test GET all documents
	results = append(results, testGetRequest("documents", "/documents"))

	// Test CREATE document with all required fields
	uniqueID := generateUniqueID()
	documentData := map[string]interface{}{
		"title":                 "Test Environmental Protection Rule " + uniqueID,
		"abstract":              "A test rule for environmental protection standards",
		"content":               "This is the full content of the test environmental protection rule...",
		"type":                  "rule",
		"status":                "draft",
		"agency_id":             1,
		"category_id":           1,
		"category_ids":          []uint{1},
		"tag_ids":               []uint{},
		"subject_ids":           []uint{},
		"publication_date":      time.Now().Format("2006-01-02"),
		"effective_date":        time.Now().AddDate(0, 1, 0).Format("2006-01-02"),
		"fr_document_number":    "TEST-" + uniqueID,
		"fr_citation":           "90 FR " + uniqueID,
		"docket_number":         "TEST-DOC-" + uniqueID,
		"regulatory_identifier": "RIN 2060-" + uniqueID,
		"accepts_comments":      true,
		"comment_instructions":  "Submit comments via regulations.gov",
		"significant_rule":      false,
		"economic_impact":       "minimal",
		"small_entity_impact":   false,
		"page_count":            5,
		"word_count":            1000,
		"language":              "en",
		"original_format":       "pdf",
		"visibility_level":      1,
		"is_public":             false,
	}

	createResult := testCreateRequest("documents", "/documents", documentData)
	results = append(results, createResult)

	if createResult.Success {
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				results = append(results, testGetRequest("documents", fmt.Sprintf("/documents/%d", id)))

				updateData := map[string]interface{}{
					"abstract": "Updated test document abstract",
					"status":   "published",
				}
				results = append(results, testUpdateRequest("documents", fmt.Sprintf("/documents/%d", id), updateData))
				results = append(results, testDeleteRequest("documents", fmt.Sprintf("/documents/%d", id)))
			}
		}
	}

	return results
}

func testRegulations() []TestResult {
	var results []TestResult

	// Test GET all regulations
	results = append(results, testGetRequest("regulations", "/regulations"))

	// Test CREATE regulation with all new fields
	regulationData := map[string]interface{}{
		"title":       "Test Air Quality Standards",
		"short_title": "Air Quality Test",
		"type":        "regulation",
		"status":      "draft",
		"description": "Test regulation for air quality standards",
		"content":     "This regulation establishes air quality standards for ambient air quality monitoring and compliance requirements.",
		"notes":       "Test regulation created by comprehensive API test",

		// Legal identifiers
		"public_law_number":     "117-58",
		"regulatory_identifier": "2040-AF33",
		"cfr_title":             "40",
		"usc_title":             "42",
		"docket_number":         "EPA-HQ-OAR-2021-0208",

		// Hierarchical structure
		"hierarchy_level": "regulation",
		"chapter_number":  "I",
		"subchapter":      "C",
		"part_number":     "50",
		"section_number":  "1",
		"subsection":      "(a)",

		// Dates
		"enactment_date":   time.Now().AddDate(-1, 0, 0).Format("2006-01-02"),
		"publication_date": time.Now().AddDate(0, -1, 0).Format("2006-01-02"),
		"effective_date":   time.Now().AddDate(0, 1, 0).Format("2006-01-02"),
		"termination_date": time.Now().AddDate(5, 0, 0).Format("2006-01-02"),

		// Relationships
		"agency_id": 1,

		// Metadata
		"is_significant": true,
	}

	createResult := testCreateRequest("regulations", "/regulations", regulationData)
	results = append(results, createResult)

	if createResult.Success {
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				results = append(results, testGetRequest("regulations", fmt.Sprintf("/regulations/%d", id)))

				updateData := map[string]interface{}{
					"description":           "Updated test regulation description with comprehensive details",
					"content":               "Updated regulation content with additional compliance requirements and monitoring procedures.",
					"notes":                 "Updated by comprehensive API test - version 1.1",
					"regulatory_identifier": "2040-AF34",
					"part_number":           "51",
					"section_number":        "2",
					"effective_date":        time.Now().AddDate(0, 2, 0).Format("2006-01-02"),
				}
				results = append(results, testUpdateRequest("regulations", fmt.Sprintf("/regulations/%d", id), updateData))
				results = append(results, testDeleteRequest("regulations", fmt.Sprintf("/regulations/%d", id)))
			}
		}
	}

	return results
}

func testProceedings() []TestResult {
	var results []TestResult

	// Test GET all proceedings
	results = append(results, testGetRequest("proceedings", "/proceedings"))

	// Test CREATE proceeding with required fields
	uniqueID := generateUniqueID()
	proceedingData := map[string]interface{}{
		"title":       "Test Environmental Rulemaking Proceeding " + uniqueID,
		"description": "Test proceeding for environmental rulemaking",
		"type":        "rulemaking",
		"status":      "planning",
		"priority":    "medium",
		"visibility":  "internal",
		"is_active":   true,
		"auto_assign": false,
		"agency_id":   1,
		"category_id": 1,
		"assigned_to": userID,
	}
	createResult := testCreateRequest("proceedings", "/proceedings", proceedingData)
	results = append(results, createResult)

	// If creation was successful, test full CRUD and additional endpoints
	if createResult.Success {
		if proceedingID := extractIDFromResponse(createResult.Response); proceedingID > 0 {
			// Test READ single proceeding
			results = append(results, testGetRequest("proceedings", fmt.Sprintf("/proceedings/%d", proceedingID)))

			// Test UPDATE proceeding
			updateData := map[string]interface{}{
				"title":       "Updated Test Environmental Rulemaking Proceeding " + uniqueID,
				"description": "Updated test proceeding description with comprehensive details",
				"priority":    "high",
				"status":      "active",
			}
			results = append(results, testUpdateRequest("proceedings", fmt.Sprintf("/proceedings/%d", proceedingID), updateData))

			// Test proceeding steps endpoints
			results = append(results, testGetRequest("proceeding_steps", fmt.Sprintf("/proceedings/%d/steps", proceedingID)))

			// Test step status update (this will likely fail if no steps exist, but we want to test the endpoint)
			stepStatusData := map[string]interface{}{
				"step_id":      1, // Test step ID
				"status":       "completed",
				"notes":        "Test step completion notes",
				"completed_by": userID,
			}
			results = append(results, testUpdateRequest("proceeding_step_status", fmt.Sprintf("/proceedings/%d/steps/1/status", proceedingID), stepStatusData))

			// Test log entry creation
			logEntryData := map[string]interface{}{
				"proceeding_id": proceedingID,
				"entry_type":    "status_change",
				"title":         "Test Log Entry",
				"description":   "Test log entry for proceeding status change",
				"metadata":      "{}",
			}
			results = append(results, testPostRequest("proceeding_logs", fmt.Sprintf("/proceedings/%d/logs", proceedingID), logEntryData))

			// Test relationships endpoint
			results = append(results, testGetRequest("proceeding_relationships", fmt.Sprintf("/proceedings/%d/relationships", proceedingID)))

			// Test linking proceeding to task (will likely fail if no tasks exist, but we want to test the endpoint)
			linkTaskData := map[string]interface{}{
				"proceeding_id": proceedingID,
				"task_id":       1, // Test task ID
				"relationship":  "related",
				"notes":         "Test proceeding-task link",
			}
			results = append(results, testPostRequest("proceeding_link_task", fmt.Sprintf("/proceedings/%d/link-task", proceedingID), linkTaskData))

			// Test linking proceeding to document
			linkDocData := map[string]interface{}{
				"proceeding_id": proceedingID,
				"document_id":   1, // Test document ID
				"relationship":  "supporting",
				"notes":         "Test proceeding-document link",
			}
			results = append(results, testPostRequest("proceeding_link_document", fmt.Sprintf("/proceedings/%d/link-document", proceedingID), linkDocData))

			// Test linking proceeding to regulation
			linkRegData := map[string]interface{}{
				"proceeding_id": proceedingID,
				"regulation_id": 1, // Test regulation ID
				"relationship":  "implements",
				"notes":         "Test proceeding-regulation link",
			}
			results = append(results, testPostRequest("proceeding_link_regulation", fmt.Sprintf("/proceedings/%d/link-regulation", proceedingID), linkRegData))

			// Test trigger review report
			results = append(results, testPostRequest("proceeding_review", fmt.Sprintf("/proceedings/%d/trigger-review", proceedingID), map[string]interface{}{}))

			// Test DELETE proceeding (at the end)
			results = append(results, testDeleteRequest("proceedings", fmt.Sprintf("/proceedings/%d", proceedingID)))
		}
	}

	return results
}

func testFinance() []TestResult {
	var results []TestResult

	// Test GET all finance records
	results = append(results, testGetRequest("finance", "/finance"))

	// Test CREATE finance record
	financeData := map[string]interface{}{
		"title":       "Test Budget Allocation",
		"description": "Test budget allocation for environmental programs",
		"type":        "original",
		"amount":      100000.50,
		"currency":    "USD",
		"fiscal_year": 2025,
		"quarter":     1,
		"status":      "draft",
		"category_id": 1,
		"agency_id":   1,
	}

	createResult := testCreateRequest("finance", "/finance", financeData)
	results = append(results, createResult)

	if createResult.Success {
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if idFloat, ok := response["id"].(float64); ok {
			id := int(idFloat)

			results = append(results, testGetRequest("finance", fmt.Sprintf("/finance/%d", id)))

			updateData := map[string]interface{}{
				"amount":      150000.75,
				"budget_type": "actual",
			}
			results = append(results, testUpdateRequest("finance", fmt.Sprintf("/finance/%d", id), updateData))
			results = append(results, testDeleteRequest("finance", fmt.Sprintf("/finance/%d", id)))
		}
	}

	return results
}

func testFinanceCategories() []TestResult {
	var results []TestResult

	// Test GET all finance categories
	results = append(results, testGetRequest("finance_categories", "/finance/categories"))

	// Test CREATE finance category
	uniqueID := generateUniqueID()
	categoryData := map[string]interface{}{
		"name":        "Test Environmental Programs " + uniqueID,
		"description": "Budget category for environmental protection programs",
		"color":       "#28a745",
	}

	createResult := testCreateRequest("finance_categories", "/finance/categories", categoryData)
	results = append(results, createResult)

	return results
}

func testUsers() []TestResult {
	var results []TestResult

	// Test GET all users (admin only)
	results = append(results, testGetRequest("users", "/users"))

	// Test CREATE user
	uniqueID := generateUniqueID()
	userData := map[string]interface{}{
		"username":     "testuser" + uniqueID,
		"email":        "testuser" + uniqueID + "@example.com",
		"first_name":   "Test",
		"last_name":    "User",
		"title":        "Test Analyst",
		"organization": "Test Organization",
		"password":     "testpass123",
		"role":         "viewer",
		"is_active":    true,
		"agency_id":    2, // Use existing agency ID
	}

	createResult := testCreateRequest("users", "/users", userData)
	results = append(results, createResult)

	if createResult.Success {
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				results = append(results, testGetRequest("users", fmt.Sprintf("/users/%d", id)))

				updateData := map[string]interface{}{
					"title": "Senior Test Analyst",
					"role":  "editor",
				}
				results = append(results, testUpdateRequest("users", fmt.Sprintf("/users/%d", id), updateData))
				results = append(results, testDeleteRequest("users", fmt.Sprintf("/users/%d", id)))
			}
		}
	}

	return results
}

func testSummaries() []TestResult {
	var results []TestResult

	// Test GET all summaries
	results = append(results, testGetRequest("summaries", "/summaries"))

	// Test CREATE summary
	summaryData := map[string]interface{}{
		"title":            "Test Environmental Policy Summary",
		"content":          "This is a test summary of environmental policy changes...",
		"abstract":         "Test summary abstract",
		"summary_type":     "update",
		"entity_type":      "document",
		"entity_id":        1,
		"action_type":      "create",
		"publication_date": time.Now().Format("2006-01-02"),
		"is_public":        true,
		"is_featured":      false,
		"priority":         2,
	}

	createResult := testCreateRequest("summaries", "/summaries", summaryData)
	results = append(results, createResult)

	if createResult.Success {
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				results = append(results, testGetRequest("summaries", fmt.Sprintf("/summaries/%d", id)))

				updateData := map[string]interface{}{
					"title":       "Updated Test Environmental Policy Summary",
					"is_featured": true,
				}
				results = append(results, testUpdateRequest("summaries", fmt.Sprintf("/summaries/%d", id), updateData))
				results = append(results, testDeleteRequest("summaries", fmt.Sprintf("/summaries/%d", id)))
			}
		}
	}

	return results
}

// makeRequestWithStatus makes HTTP request and returns response with status code
func makeRequestWithStatus(method, endpoint string, data interface{}, token string) (string, int, error) {
	// Add small delay to avoid rate limiting
	time.Sleep(20 * time.Millisecond)

	var body io.Reader
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return "", 0, err
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, baseURL+endpoint, body)
	if err != nil {
		return "", 0, err
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
		fmt.Printf("🔑 DEBUG: Sending token: %s...\n", token[:20])
	} else {
		fmt.Printf("⚠️ DEBUG: No token provided\n")
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", 0, err
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", resp.StatusCode, err
	}

	return string(responseBody), resp.StatusCode, nil
}

// Helper functions for making HTTP requests (backward compatibility)
func makeRequest(method, endpoint string, data interface{}, token string) (string, error) {
	response, _, err := makeRequestWithStatus(method, endpoint, data, token)
	return response, err
}

func testGetRequest(entity, endpoint string) TestResult {
	// Add small delay to prevent rate limiting
	time.Sleep(100 * time.Millisecond)

	resp, statusCode, err := makeRequestWithStatus("GET", endpoint, nil, authToken)
	success := err == nil && statusCode >= 200 && statusCode < 300
	errorMsg := ""

	if err != nil {
		errorMsg = err.Error()
	} else if statusCode >= 400 {
		errorMsg = fmt.Sprintf("HTTP %d error", statusCode)
		if len(resp) > 0 && len(resp) < 200 {
			errorMsg += ": " + resp
		}
	}

	return TestResult{
		Entity:     entity,
		Operation:  "GET",
		Success:    success,
		Error:      errorMsg,
		Response:   resp,
		Endpoint:   endpoint,
		Timestamp:  time.Now(),
		StatusCode: statusCode,
	}
}

func testCreateRequest(entity, endpoint string, data interface{}) TestResult {
	// Add delay to prevent rate limiting
	time.Sleep(500 * time.Millisecond)

	resp, statusCode, err := makeRequestWithStatus("POST", endpoint, data, authToken)
	success := err == nil && statusCode >= 200 && statusCode < 300
	errorMsg := ""

	if err != nil {
		errorMsg = err.Error()
	} else if statusCode >= 400 {
		errorMsg = fmt.Sprintf("HTTP %d error", statusCode)
		if len(resp) > 0 && len(resp) < 200 {
			errorMsg += ": " + resp
		}
	} else if contains(resp, "error") {
		errorMsg = "API returned error response"
		// Include response details for debugging
		if len(resp) < 200 {
			errorMsg += "\n     Response: " + resp
		}
	}

	return TestResult{
		Entity:     entity,
		Operation:  "CREATE",
		Success:    success,
		Error:      errorMsg,
		Response:   resp,
		Endpoint:   endpoint,
		Timestamp:  time.Now(),
		StatusCode: statusCode,
	}
}

func testPostRequest(entity, endpoint string, data interface{}) TestResult {
	// Add delay to prevent rate limiting
	time.Sleep(300 * time.Millisecond)

	resp, statusCode, err := makeRequestWithStatus("POST", endpoint, data, authToken)
	success := err == nil && statusCode >= 200 && statusCode < 300
	errorMsg := ""

	if err != nil {
		errorMsg = err.Error()
	} else if statusCode >= 400 {
		errorMsg = fmt.Sprintf("HTTP %d error", statusCode)
		if len(resp) > 0 && len(resp) < 200 {
			errorMsg += ": " + resp
		}
	} else if contains(resp, "error") {
		errorMsg = "API returned error response"
		// Include response details for debugging
		if len(resp) < 200 {
			errorMsg += "\n     Response: " + resp
		}
	}

	return TestResult{
		Entity:     entity,
		Operation:  "POST",
		Success:    success,
		Error:      errorMsg,
		Response:   resp,
		Endpoint:   endpoint,
		Timestamp:  time.Now(),
		StatusCode: statusCode,
	}
}

func testUpdateRequest(entity, endpoint string, data interface{}) TestResult {
	// Add delay to prevent rate limiting
	time.Sleep(300 * time.Millisecond)

	resp, statusCode, err := makeRequestWithStatus("PUT", endpoint, data, authToken)
	success := err == nil && statusCode >= 200 && statusCode < 300
	errorMsg := ""

	if err != nil {
		errorMsg = err.Error()
	} else if statusCode >= 400 {
		errorMsg = fmt.Sprintf("HTTP %d error", statusCode)
		if len(resp) > 0 && len(resp) < 200 {
			errorMsg += ": " + resp
		}
	} else if contains(resp, "error") {
		errorMsg = "API returned error response"
		// Include response details for debugging
		if len(resp) < 200 {
			errorMsg += "\n     Response: " + resp
		}
	}

	return TestResult{
		Entity:     entity,
		Operation:  "UPDATE",
		Success:    success,
		Error:      errorMsg,
		Response:   resp,
		Endpoint:   endpoint,
		Timestamp:  time.Now(),
		StatusCode: statusCode,
	}
}

func testDeleteRequest(entity, endpoint string) TestResult {
	// Add delay to prevent rate limiting
	time.Sleep(300 * time.Millisecond)

	resp, statusCode, err := makeRequestWithStatus("DELETE", endpoint, nil, authToken)
	success := err == nil && statusCode >= 200 && statusCode < 300
	errorMsg := ""

	if err != nil {
		errorMsg = err.Error()
	} else if statusCode >= 400 {
		errorMsg = fmt.Sprintf("HTTP %d error", statusCode)
		if len(resp) > 0 && len(resp) < 200 {
			errorMsg += ": " + resp
		}
	} else if contains(resp, "error") {
		errorMsg = "API returned error response"
		// Include response details for debugging
		if len(resp) < 200 {
			errorMsg += "\n     Response: " + resp
		}
	}

	return TestResult{
		Entity:     entity,
		Operation:  "DELETE",
		Success:    success,
		Error:      errorMsg,
		Response:   resp,
		Endpoint:   endpoint,
		Timestamp:  time.Now(),
		StatusCode: statusCode,
	}
}

func contains(s, substr string) bool {
	return len(s) > 0 && len(substr) > 0 &&
		(s == substr || len(s) >= len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
				findInString(s, substr)))
}

func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func generateReport(results []TestResult) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📊 COMPREHENSIVE API TEST REPORT")
	fmt.Println(strings.Repeat("=", 80))

	totalTests := len(results)
	successfulTests := 0
	failedTests := 0

	entityStats := make(map[string]map[string]int)

	for _, result := range results {
		if entityStats[result.Entity] == nil {
			entityStats[result.Entity] = make(map[string]int)
		}

		if result.Success {
			successfulTests++
			entityStats[result.Entity]["success"]++
		} else {
			failedTests++
			entityStats[result.Entity]["failed"]++
		}
		entityStats[result.Entity]["total"]++
	}

	// Overall summary
	fmt.Printf("\n📈 OVERALL SUMMARY:\n")
	fmt.Printf("   Total Tests: %d\n", totalTests)
	fmt.Printf("   ✅ Successful: %d (%.1f%%)\n", successfulTests, float64(successfulTests)/float64(totalTests)*100)
	fmt.Printf("   ❌ Failed: %d (%.1f%%)\n", failedTests, float64(failedTests)/float64(totalTests)*100)

	// Entity breakdown
	fmt.Printf("\n📋 ENTITY BREAKDOWN:\n")
	for entity, stats := range entityStats {
		total := stats["total"]
		success := stats["success"]
		failed := stats["failed"]
		successRate := float64(success) / float64(total) * 100

		status := "✅"
		if failed > 0 {
			status = "⚠️"
		}
		if failed > success {
			status = "❌"
		}

		fmt.Printf("   %s %-20s: %d/%d (%.1f%% success)\n",
			status, entity, success, total, successRate)
	}

	// Failed tests details
	if failedTests > 0 {
		fmt.Printf("\n❌ FAILED TESTS DETAILS:\n")
		for _, result := range results {
			if !result.Success {
				fmt.Printf("   • %s %s: %s\n", result.Entity, result.Operation, result.Error)
				if len(result.Response) > 0 && len(result.Response) < 200 {
					fmt.Printf("     Response: %s\n", result.Response)
				}
			}
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("🎯 Test completed! Check the results above for any issues.")
	fmt.Println(strings.Repeat("=", 80))
}

func testTasks() []TestResult {
	var results []TestResult

	// Test GET all tasks
	results = append(results, testGetRequest("tasks", "/tasks"))

	// Test CREATE task
	taskData := map[string]interface{}{
		"title":       "Test Document Review Task",
		"description": "Review the test environmental protection document",
		"status":      "pending",
		"priority":    "medium",
		"due_date":    time.Now().AddDate(0, 0, 7).Format("2006-01-02T15:04:05Z07:00"),
		"assigned_to": userID,
		"created_by":  userID,
		"category_id": 1,
		"entity_type": "document",
		"entity_id":   1,
	}

	createResult := testCreateRequest("tasks", "/tasks", taskData)
	results = append(results, createResult)

	if createResult.Success {
		var response map[string]interface{}
		json.Unmarshal([]byte(createResult.Response), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if idFloat, ok := data["id"].(float64); ok {
				id := int(idFloat)

				results = append(results, testGetRequest("tasks", fmt.Sprintf("/tasks/%d", id)))

				updateData := map[string]interface{}{
					"description": "Updated task description",
					"priority":    "high",
				}
				results = append(results, testUpdateRequest("tasks", fmt.Sprintf("/tasks/%d", id), updateData))
				results = append(results, testDeleteRequest("tasks", fmt.Sprintf("/tasks/%d", id)))
			}
		}
	}

	return results
}

func testCalendar() []TestResult {
	var results []TestResult

	// Test GET calendar
	results = append(results, testGetRequest("calendar", "/calendar"))

	// Test GET calendar stats
	results = append(results, testGetRequest("calendar_stats", "/calendar/stats"))

	// Test calendar events CRUD
	eventData := map[string]interface{}{
		"title":       "Test Calendar Event",
		"description": "Test event description",
		"type":        "hearing",
		"start_date":  time.Now().AddDate(0, 0, 30).Format("2006-01-02T15:04:05Z07:00"),
		"end_date":    time.Now().AddDate(0, 0, 30).Add(2 * time.Hour).Format("2006-01-02T15:04:05Z07:00"),
		"location":    "Test Location",
		"is_public":   true,
		"priority":    "medium",
	}

	createResult := testCreateRequest("calendar_events", "/calendar/events", eventData)
	results = append(results, createResult)

	// If creation was successful, test read, update, delete
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("calendar_events", fmt.Sprintf("/calendar/events/%d", id)))

			updateData := map[string]interface{}{
				"title":       "Updated Test Calendar Event",
				"description": "Updated test event description",
				"type":        "deadline",
				"start_date":  time.Now().AddDate(0, 0, 35).Format("2006-01-02T15:04:05Z07:00"),
				"end_date":    time.Now().AddDate(0, 0, 35).Add(2 * time.Hour).Format("2006-01-02T15:04:05Z07:00"),
				"location":    "Updated Test Location",
				"is_public":   true,
				"priority":    "high",
			}
			results = append(results, testUpdateRequest("calendar_events", fmt.Sprintf("/calendar/events/%d", id), updateData))
			results = append(results, testDeleteRequest("calendar_events", fmt.Sprintf("/calendar/events/%d", id)))
		}
	}

	return results
}

func testSearch() []TestResult {
	var results []TestResult

	// Test general search
	results = append(results, testGetRequest("search", "/search?q=test"))
	results = append(results, testGetRequest("search_documents", "/search?q=test&type=documents"))
	results = append(results, testGetRequest("search_agencies", "/search?q=test&type=agencies"))
	results = append(results, testGetRequest("search_categories", "/search?q=test&type=categories"))
	results = append(results, testGetRequest("search_regulations", "/search?q=test&type=regulations"))
	results = append(results, testGetRequest("search_tasks", "/search?q=test&type=tasks"))
	results = append(results, testGetRequest("search_calendar", "/search?q=test&type=calendar"))
	results = append(results, testGetRequest("search_proceedings", "/search?q=test&type=proceedings"))
	results = append(results, testGetRequest("search_users", "/search?q=test&type=users"))

	return results
}

func testTags() []TestResult {
	var results []TestResult

	// Test GET all tags
	results = append(results, testGetRequest("tags", "/tags"))

	// Test tag CRUD
	tagData := map[string]interface{}{
		"name":        "Test Tag",
		"slug":        "test-tag",
		"description": "Test tag description",
		"color":       "#FF5733",
		"is_public":   true,
	}

	createResult := testCreateRequest("tags", "/tags", tagData)
	results = append(results, createResult)

	// If creation was successful, test read, update, delete
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("tags", fmt.Sprintf("/tags/%d", id)))

			updateData := map[string]interface{}{
				"name":        fmt.Sprintf("Updated Test Tag %d", time.Now().Unix()),
				"slug":        fmt.Sprintf("updated-test-tag-%d", time.Now().Unix()),
				"description": "Updated test tag description",
				"color":       "#33FF57",
				"is_public":   true,
			}
			results = append(results, testUpdateRequest("tags", fmt.Sprintf("/tags/%d", id), updateData))
			results = append(results, testDeleteRequest("tags", fmt.Sprintf("/tags/%d", id)))
		}
	}

	return results
}

func testSubjects() []TestResult {
	var results []TestResult

	// Test GET all subjects
	results = append(results, testGetRequest("subjects", "/subjects"))

	// Test subject CRUD
	uniqueID := generateUniqueID()
	subjectData := map[string]interface{}{
		"name":        fmt.Sprintf("Test Subject %s", uniqueID),
		"slug":        fmt.Sprintf("test-subject-%s", uniqueID),
		"description": "Test subject description",
		"code":        fmt.Sprintf("TEST%s", uniqueID[:6]),
		"is_active":   true,
	}

	createResult := testCreateRequest("subjects", "/subjects", subjectData)
	results = append(results, createResult)

	// If creation was successful, test read, update, delete
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("subjects", fmt.Sprintf("/subjects/%d", id)))

			updateData := map[string]interface{}{
				"name":        fmt.Sprintf("Updated Test Subject %d", time.Now().Unix()),
				"slug":        fmt.Sprintf("updated-test-subject-%d", time.Now().Unix()),
				"description": "Updated test subject description",
				"code":        fmt.Sprintf("TEST%d", time.Now().Unix()%10000),
				"is_active":   true,
			}
			results = append(results, testUpdateRequest("subjects", fmt.Sprintf("/subjects/%d", id), updateData))
			results = append(results, testDeleteRequest("subjects", fmt.Sprintf("/subjects/%d", id)))
		}
	}

	return results
}

func testComments() []TestResult {
	var results []TestResult

	// Comments are typically accessed through documents, so we'll test that
	// First get a document to comment on
	docResp, err := makeRequest("GET", "/documents", nil, authToken)
	if err == nil {
		var docResponse struct {
			Data []struct {
				ID uint `json:"id"`
			} `json:"data"`
		}
		if json.Unmarshal([]byte(docResp), &docResponse) == nil && len(docResponse.Data) > 0 {
			docID := docResponse.Data[0].ID

			// Test creating a comment
			commentData := map[string]interface{}{
				"content":   "This is a test comment",
				"is_public": true,
			}
			results = append(results, testCreateRequest("comments", fmt.Sprintf("/documents/%d/comments", docID), commentData))

			// Test getting comments for document
			results = append(results, testGetRequest("comments", fmt.Sprintf("/documents/%d/comments", docID)))
		}
	}

	return results
}

func testAnalytics() []TestResult {
	var results []TestResult

	// Test analytics endpoints (admin only)
	results = append(results, testGetRequest("analytics_dashboard", "/analytics/dashboard"))
	results = append(results, testGetRequest("analytics_documents", "/analytics/documents/stats"))
	results = append(results, testGetRequest("analytics_agencies", "/analytics/agencies/stats"))
	results = append(results, testGetRequest("analytics_users", "/analytics/users/stats"))
	results = append(results, testGetRequest("analytics_relationships", "/analytics/relationships/stats"))

	return results
}

func testRoles() []TestResult {
	var results []TestResult

	// Test GET all roles
	results = append(results, testGetRequest("roles", "/admin/roles"))

	// Test role CRUD
	uniqueID := generateUniqueID()
	roleData := map[string]interface{}{
		"name":         fmt.Sprintf("Test Role %s", uniqueID),
		"display_name": fmt.Sprintf("Test Role Display %s", uniqueID),
		"description":  "Test role description",
		"permissions":  []string{"read_documents", "create_documents"},
		"is_active":    true,
	}

	createResult := testCreateRequest("roles", "/admin/roles", roleData)
	results = append(results, createResult)

	// If creation was successful, test read, update, delete
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("roles", fmt.Sprintf("/admin/roles/%d", id)))

			updateData := map[string]interface{}{
				"name":         fmt.Sprintf("Updated Test Role %d", time.Now().Unix()),
				"display_name": fmt.Sprintf("Updated Test Role Display %d", time.Now().Unix()),
				"description":  "Updated test role description",
				"permissions":  []string{"read_documents", "create_documents", "update_documents"},
				"is_active":    true,
			}
			results = append(results, testUpdateRequest("roles", fmt.Sprintf("/admin/roles/%d", id), updateData))
			results = append(results, testDeleteRequest("roles", fmt.Sprintf("/admin/roles/%d", id)))
		}
	}

	// Test permissions endpoint
	results = append(results, testGetRequest("permissions", "/admin/permissions"))

	return results
}

func testSignatures() []TestResult {
	var results []TestResult

	// First create a test document to use for signatures
	uniqueID := generateUniqueID()
	documentData := map[string]interface{}{
		"title":                 "Test Document for Signatures " + uniqueID,
		"abstract":              "Test document abstract for signature testing",
		"content":               "This is a test document for signature testing",
		"type":                  "rule",
		"agency_id":             1,
		"category_ids":          []uint{1},
		"tag_ids":               []uint{},
		"subject_ids":           []uint{},
		"publication_date":      time.Now().Format("2006-01-02"),
		"effective_date":        time.Now().AddDate(0, 1, 0).Format("2006-01-02"),
		"fr_document_number":    "SIG-TEST-" + uniqueID,
		"fr_citation":           "90 FR " + uniqueID,
		"docket_number":         "SIG-DOC-" + uniqueID,
		"regulatory_identifier": "RIN 2060-" + uniqueID,
		"accepts_comments":      false,
		"significant_rule":      false,
		"economic_impact":       "minimal",
		"small_entity_impact":   false,
		"page_count":            1,
		"word_count":            100,
		"language":              "en",
		"original_format":       "pdf",
		"visibility_level":      1,
		"is_public":             false,
	}

	createDocResult := testCreateRequest("signatures", "/documents", documentData)
	results = append(results, createDocResult)

	// Only proceed with signature test if document creation was successful
	if createDocResult.Success {
		docID := extractIDFromResponse(createDocResult.Response)
		if docID > 0 {
			// Test signature request creation
			signatureData := map[string]interface{}{
				"document_id":      docID,
				"requested_by_id":  userID,
				"signer_id":        userID,
				"signature_type":   "digital",
				"required_by_date": time.Now().AddDate(0, 0, 7).Format("2006-01-02T15:04:05Z07:00"),
				"instructions":     "Please sign this test document",
				"is_urgent":        false,
			}

			results = append(results, testCreateRequest("signatures", "/signatures/request", signatureData))
		}
	}

	// Test getting user signatures
	results = append(results, testGetRequest("signatures", "/signatures/my"))

	return results
}

func testCertificates() []TestResult {
	var results []TestResult

	// Test GET all certificates
	results = append(results, testGetRequest("certificates", "/certificates"))

	// Test certificate creation with correct fields based on actual model
	certData := map[string]interface{}{
		"subject":             "CN=Test Certificate,O=Test Organization,C=US",
		"common_name":         "Test Certificate",
		"organization":        "Test Organization",
		"organizational_unit": "IT Department",
		"country":             "US",
		"state":               "California",
		"locality":            "San Francisco",
		"email_address":       "<EMAIL>",
		"key_algorithm":       "RSA",
		"key_length":          2048,
		"validity_period":     365,
		"purpose":             "signing",
		"notes":               "Test certificate for API testing",
	}
	createResult := testCreateRequest("certificates", "/certificates", certData)
	results = append(results, createResult)

	// Test dependent operations if creation was successful
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("certificates", fmt.Sprintf("/certificates/%d", id)))

			updateData := map[string]interface{}{
				"purpose": "document_signing",
				"notes":   "Updated test certificate for document signing",
			}
			results = append(results, testUpdateRequest("certificates", fmt.Sprintf("/certificates/%d", id), updateData))

			// Test certificate revocation
			revokeData := map[string]interface{}{
				"revocation_reason": "Testing certificate revocation",
			}
			results = append(results, testPostRequest("certificates", fmt.Sprintf("/certificates/%d/revoke", id), revokeData))
		}
	}

	return results
}

func testRetentionPolicies() []TestResult {
	var results []TestResult

	// Test GET all retention policies
	results = append(results, testGetRequest("retention_policies", "/retention-policies"))

	// Test retention policy creation
	policyData := map[string]interface{}{
		"name":           "Test Retention Policy",
		"description":    "Test retention policy description",
		"entity_type":    "documents",
		"retention_days": 365,
		"is_active":      true,
		"auto_delete":    false,
	}

	createResult := testCreateRequest("retention_policies", "/retention-policies", policyData)
	results = append(results, createResult)

	// If creation was successful, test read, update, delete
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("retention_policies", fmt.Sprintf("/retention-policies/%d", id)))

			updateData := map[string]interface{}{
				"name":           "Updated Test Retention Policy",
				"description":    "Updated test retention policy description",
				"entity_type":    "documents",
				"retention_days": 730,
				"auto_delete":    true,
				"is_active":      true,
			}
			results = append(results, testUpdateRequest("retention_policies", fmt.Sprintf("/retention-policies/%d", id), updateData))
			results = append(results, testDeleteRequest("retention_policies", fmt.Sprintf("/retention-policies/%d", id)))
		}
	}

	return results
}

func testProcessing() []TestResult {
	var results []TestResult

	// Test GET all processing jobs
	results = append(results, testGetRequest("processing_jobs", "/processing/jobs"))

	// First create a test document for processing
	uniqueID := generateUniqueID()
	documentData := map[string]interface{}{
		"title":              "Test Document for Processing " + uniqueID,
		"content":            "This is a test document for processing job testing",
		"type":               "rule",
		"agency_id":          2, // Use existing agency ID
		"status":             "draft",
		"fr_document_number": "PROC-TEST-" + uniqueID, // Unique FR number
	}

	createDocResult := testCreateRequest("processing_jobs", "/documents", documentData)
	results = append(results, createDocResult)

	// Only proceed with processing job test if document creation was successful
	if createDocResult.Success {
		docID := extractIDFromResponse(createDocResult.Response)
		if docID > 0 {
			// Test processing job creation
			jobData := map[string]interface{}{
				"name":             "Test Processing Job",
				"type":             "text_extraction",
				"priority":         2,
				"document_id":      docID,
				"engine":           "tesseract",
				"configuration":    `{"analyze_sentiment": true}`,
				"input_format":     "pdf",
				"output_format":    "text",
				"language":         "en",
				"extract_metadata": true,
			}

			createResult := testCreateRequest("processing_jobs", "/processing/jobs", jobData)
			results = append(results, createResult)
		}
	}

	// Test processing template creation
	templateData := map[string]interface{}{
		"name":              "Test Processing Template",
		"description":       "Test processing template description",
		"processing_types":  []string{"text_analysis", "sentiment_analysis"}, // Array of strings
		"default_engine":    "tesseract",
		"configuration":     `{"default_settings": true, "analyze_sentiment": true}`,
		"supported_formats": []string{"pdf", "docx", "txt"},
		"max_file_size":     10485760, // 10MB
		"is_active":         true,
	}

	results = append(results, testCreateRequest("processing_templates", "/processing/templates", templateData))

	return results
}

// Helper function to extract ID from JSON response
func extractIDFromResponse(response string) uint {
	var result struct {
		Data struct {
			ID uint `json:"id"`
		} `json:"data"`
		ID uint `json:"id"` // Some responses have ID at root level
	}

	if err := json.Unmarshal([]byte(response), &result); err == nil {
		if result.Data.ID > 0 {
			return result.Data.ID
		}
		return result.ID
	}
	return 0
}

// testEnterpriseEndpoints tests enterprise-level endpoints
func testEnterpriseEndpoints() []TestResult {
	var results []TestResult

	// Test Enterprise Content Management
	results = append(results, testGetRequest("enterprise", "/enterprise/content/repositories"))

	// Test Enterprise Business Intelligence
	results = append(results, testGetRequest("enterprise", "/enterprise/bi/warehouses"))
	results = append(results, testGetRequest("enterprise", "/enterprise/bi/data-sources"))
	results = append(results, testGetRequest("enterprise", "/enterprise/bi/dashboards"))
	results = append(results, testGetRequest("enterprise", "/enterprise/bi/reports"))
	results = append(results, testGetRequest("enterprise", "/enterprise/bi/kpis"))

	// Test Enterprise Compliance
	results = append(results, testGetRequest("enterprise", "/enterprise/compliance/requirements"))
	results = append(results, testGetRequest("enterprise", "/enterprise/compliance/audits"))
	results = append(results, testGetRequest("enterprise", "/enterprise/compliance/policies"))

	// Test Enterprise HR
	results = append(results, testGetRequest("enterprise", "/enterprise/hr/employees"))
	results = append(results, testGetRequest("enterprise", "/enterprise/hr/departments"))
	results = append(results, testGetRequest("enterprise", "/enterprise/hr/positions"))

	// Test Enterprise Financial
	results = append(results, testGetRequest("enterprise", "/enterprise/financial/budgets"))
	results = append(results, testGetRequest("enterprise", "/enterprise/financial/expenses"))
	results = append(results, testGetRequest("enterprise", "/enterprise/financial/reports"))

	// Test Enterprise KPI creation
	kpiData := map[string]interface{}{
		"kpi_name":         "Test Document Processing KPI",
		"description":      "Test KPI for measuring document processing efficiency",
		"category":         "operational",
		"formula":          "processed_documents / total_documents * 100",
		"data_source":      "document_processing_metrics",
		"unit":             "%",
		"target_value":     85.0,
		"min_threshold":    70.0,
		"max_threshold":    95.0,
		"critical_min":     50.0,
		"critical_max":     100.0,
		"current_value":    75.0,
		"update_frequency": "daily",
		"owner_id":         userID,
		"is_active":        true,
		"metadata":         `{"source": "test", "automated": true}`,
	}
	createResult := testCreateRequest("enterprise_kpi", "/enterprise/bi/kpis", kpiData)
	results = append(results, createResult)

	// Test KPI operations if creation was successful
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("enterprise_kpi", fmt.Sprintf("/enterprise/bi/kpis/%d", id)))

			updateData := map[string]interface{}{
				"current_value":    80.0,
				"target_value":     90.0,
				"update_frequency": "weekly",
			}
			results = append(results, testUpdateRequest("enterprise_kpi", fmt.Sprintf("/enterprise/bi/kpis/%d", id), updateData))
			results = append(results, testDeleteRequest("enterprise_kpi", fmt.Sprintf("/enterprise/bi/kpis/%d", id)))
		}
	}

	return results
}

// testPublicEndpoints tests all public endpoints (no auth required)
func testPublicEndpoints() []TestResult {
	var results []TestResult

	// Test public document endpoints
	results = append(results, testGetRequestNoAuth("public", "/public/documents"))
	results = append(results, testGetRequestNoAuth("public", "/public/documents/search?q=test"))
	results = append(results, testGetRequestNoAuth("public", "/public/stats"))

	// Test public agency endpoints
	results = append(results, testGetRequestNoAuth("public", "/public/agencies"))
	results = append(results, testGetRequestNoAuth("public", "/public/agencies/1"))
	results = append(results, testGetRequestNoAuth("public", "/public/agencies/1/documents"))

	// Test public category endpoints
	results = append(results, testGetRequestNoAuth("public", "/public/categories"))
	results = append(results, testGetRequestNoAuth("public", "/public/categories/1"))
	results = append(results, testGetRequestNoAuth("public", "/public/categories/1/documents"))

	// Test public subject endpoints
	results = append(results, testGetRequestNoAuth("public", "/public/subjects"))
	results = append(results, testGetRequestNoAuth("public", "/public/subjects/1/documents"))

	// Test public calendar endpoints
	results = append(results, testGetRequestNoAuth("public", "/public/calendar"))
	results = append(results, testGetRequestNoAuth("public", "/public/calendar/stats"))

	// Test public tag endpoints
	results = append(results, testGetRequestNoAuth("public", "/public/tags"))
	results = append(results, testGetRequestNoAuth("public", "/public/tags/1/documents"))

	return results
}

// testGetRequestNoAuth makes a GET request without authentication
func testGetRequestNoAuth(entity, endpoint string) TestResult {
	// Add small delay to prevent rate limiting
	time.Sleep(100 * time.Millisecond)

	resp, statusCode, err := makeRequestWithStatus("GET", endpoint, nil, "")
	success := err == nil && statusCode >= 200 && statusCode < 300

	var errorMsg string
	if err != nil {
		errorMsg = err.Error()
	} else if statusCode >= 400 {
		errorMsg = fmt.Sprintf("HTTP %d error", statusCode)
		if len(resp) > 0 && len(resp) < 200 {
			errorMsg += ": " + resp
		}
	}

	return TestResult{
		Entity:     entity,
		Operation:  "GET_NO_AUTH",
		Success:    success,
		Error:      errorMsg,
		Response:   resp,
		Endpoint:   endpoint,
		Timestamp:  time.Now(),
		StatusCode: statusCode,
	}
}

// testDocumentFiles tests document file management endpoints
func testDocumentFiles() []TestResult {
	var results []TestResult

	// Test document file upload (would need actual file in real scenario)
	results = append(results, testGetRequest("document_files", "/documents/1/files"))

	// Test file metadata
	results = append(results, testGetRequest("document_files", "/documents/1/metadata"))

	return results
}

// testDocumentAnalysis tests document analysis endpoints
func testDocumentAnalysis() []TestResult {
	var results []TestResult

	// Test document analysis endpoints
	results = append(results, testGetRequest("document_analysis", "/documents/1/metadata"))
	results = append(results, testGetRequest("document_analysis", "/documents/1/classification"))
	results = append(results, testGetRequest("document_analysis", "/documents/1/entities"))

	return results
}

// testInterconnect tests interconnect/NLP endpoints
func testInterconnect() []TestResult {
	var results []TestResult

	// Test NLP parsing
	parseData := map[string]interface{}{
		"text": "Create a task to review environmental regulations by next Friday",
	}
	results = append(results, testPostRequest("interconnect", "/tasks/parse-text", parseData))

	return results
}

// testAutoGeneration tests auto-generation endpoints
func testAutoGeneration() []TestResult {
	var results []TestResult

	// Test auto-generation
	genData := map[string]interface{}{
		"entity_type": "document",
		"entity_id":   1,
		"config": map[string]interface{}{
			"generate_abstract": true,
			"generate_summary":  true,
		},
	}
	results = append(results, testPostRequest("auto_generation", "/auto-generation/generate", genData))

	return results
}

// testRegulationChunks tests regulation chunk management
func testRegulationChunks() []TestResult {
	var results []TestResult

	// Test regulation chunks GET
	results = append(results, testGetRequest("regulation_chunks", "/regulations/1/chunks"))

	// Test regulation chunk creation (regulation ID comes from URL parameter)
	chunkData := map[string]interface{}{
		"chunk_order": 1,
		"title":       "Test Regulation Chunk Section",
		"content":     "This is a test regulation chunk content for section 1.1 of the environmental protection regulations.",
		"chunk_type":  "section",
		"is_active":   true,
	}
	createResult := testCreateRequest("regulation_chunks", "/regulations/1/chunks", chunkData)
	results = append(results, createResult)

	// Test chunk operations if creation was successful
	if createResult.Success {
		if id := extractIDFromResponse(createResult.Response); id > 0 {
			results = append(results, testGetRequest("regulation_chunks", fmt.Sprintf("/regulations/1/chunks/%d", id)))

			updateData := map[string]interface{}{
				"title":      "Updated Test Regulation Chunk Section",
				"content":    "Updated content for the test regulation chunk with additional compliance requirements.",
				"chunk_type": "subsection",
			}
			results = append(results, testUpdateRequest("regulation_chunks", fmt.Sprintf("/regulations/1/chunks/%d", id), updateData))
			results = append(results, testDeleteRequest("regulation_chunks", fmt.Sprintf("/regulations/1/chunks/%d", id)))
		}
	}

	return results
}

// testRegulationRelationships tests regulation relationship endpoints
func testRegulationRelationships() []TestResult {
	var results []TestResult

	// Test regulation relationships
	results = append(results, testGetRequest("regulation_relationships", "/regulations/1/relationships"))

	return results
}

// testTaskPerformance tests task performance evaluation endpoints
func testTaskPerformance() []TestResult {
	var results []TestResult

	// Test task performance endpoints
	results = append(results, testGetRequest("task_performance", "/tasks/performance/dashboard"))
	results = append(results, testGetRequest("task_performance", "/tasks/performance/metrics"))

	return results
}
