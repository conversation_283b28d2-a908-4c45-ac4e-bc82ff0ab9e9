"use strict";
exports.id = "vendor-chunks/micromark-util-decode-numeric-character-reference";
exports.ids = ["vendor-chunks/micromark-util-decode-numeric-character-reference"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   decodeNumericCharacterReference: () => (/* binding */ decodeNumericCharacterReference)
/* harmony export */ });
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/values.js");


/**
 * Turn the number (in string form as either hexa- or plain decimal) coming from
 * a numeric character reference into a character.
 *
 * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes
 * non-characters and control characters safe.
 *
 * @param {string} value
 *   Value to decode.
 * @param {number} base
 *   Numeric base.
 * @returns {string}
 *   Character.
 */
function decodeNumericCharacterReference(value, base) {
  const code = Number.parseInt(value, base)

  if (
    // C0 except for HT, LF, FF, CR, space.
    code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht ||
    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.vt ||
    (code > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.cr && code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space) ||
    // Control character (DEL) of C0, and C1 controls.
    (code > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde && code < 160) ||
    // Lone high surrogates and low surrogates.
    (code > 55_295 && code < 57_344) ||
    // Noncharacters.
    (code > 64_975 && code < 65_008) ||
    /* eslint-disable no-bitwise */
    (code & 65_535) === 65_535 ||
    (code & 65_535) === 65_534 ||
    /* eslint-enable no-bitwise */
    // Out of range
    code > 1_114_111
  ) {
    return micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.values.replacementCharacter
  }

  return String.fromCodePoint(code)
}


/***/ })

};
;