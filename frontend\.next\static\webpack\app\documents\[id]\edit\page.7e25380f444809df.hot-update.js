"use strict";
self["webpackHotUpdate_N_E"]("app/documents/[id]/edit/page",{

/***/ "(app-pages-browser)/./app/documents/[id]/edit/page.tsx":
/*!******************************************!*\
  !*** ./app/documents/[id]/edit/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ "(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js");
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js");
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js");
/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js");
/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js");
/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js");
/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/Layout/Layout */ "(app-pages-browser)/./app/components/Layout/Layout.tsx");
/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../stores/authStore */ "(app-pages-browser)/./app/stores/authStore.ts");
/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../services/api */ "(app-pages-browser)/./app/services/api.ts");
/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();



var _jsxFileName = "C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\documents\\[id]\\edit\\page.tsx", _this = undefined;
var __jsx = (react__WEBPACK_IMPORTED_MODULE_3___default().createElement);
function ownKeys(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {
            (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}








var EditDocumentPage = function EditDocumentPage() {
    _s();
    var params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useParams)();
    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();
    var _useAuthStore = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_8__.useAuthStore)(), user = _useAuthStore.user, isAuthenticated = _useAuthStore.isAuthenticated;
    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true), loading = _useState[0], setLoading = _useState[1];
    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), saving = _useState2[0], setSaving = _useState2[1];
    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''), error = _useState3[0], setError = _useState3[1];
    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''), success = _useState4[0], setSuccess = _useState4[1];
    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), agencies = _useState5[0], setAgencies = _useState5[1];
    var _useState6 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), categories = _useState6[0], setCategories = _useState6[1];
    var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), tags = _useState7[0], setTags = _useState7[1];
    var _useState8 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), subjects = _useState8[0], setSubjects = _useState8[1];
    var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), document = _useState9[0], setDocument = _useState9[1];
    var _useState0 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
        title: '',
        "abstract": '',
        content: '',
        type: 'rule',
        agency_id: '',
        category_ids: [],
        tag_ids: [],
        subject_ids: [],
        publication_date: '',
        effective_date: '',
        termination_date: '',
        comment_due_date: '',
        fr_document_number: '',
        fr_citation: '',
        cfr_citations: '',
        docket_number: '',
        regulatory_identifier: '',
        accepts_comments: true,
        comment_instructions: '',
        public_hearing_date: '',
        public_hearing_info: '',
        page_count: 0,
        word_count: 0,
        language: 'en',
        original_format: 'pdf',
        file_size: 0,
        checksum: '',
        visibility_level: 1,
        is_public: true,
        significant_rule: false,
        economic_impact: '',
        small_entity_impact: false
    }), formData = _useState0[0], setFormData = _useState0[1];
    var documentId = params === null || params === void 0 ? void 0 : params.id;
    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({
        "EditDocumentPage.useEffect": function() {
            if (!isAuthenticated || (user === null || user === void 0 ? void 0 : user.role) === 'viewer') {
                router.push('/dashboard');
                return;
            }
            var fetchData = /*#__PURE__*/ ({
                "EditDocumentPage.useEffect.fetchData": function() {
                    var _ref = (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__["default"])(/*#__PURE__*/ C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
                        var _documentResponse$age, _documentResponse$cat, _documentResponse$tag, _documentResponse$sub, documentResponse, agenciesResponse, categoriesResponse, tagsResponse, subjectsResponse, _err$response;
                        return C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {
                            while(1)switch(_context.prev = _context.next){
                                case 0:
                                    _context.prev = 0;
                                    setLoading(true);
                                    // Fetch document
                                    _context.next = 4;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_9__["default"].getDocument(parseInt(documentId));
                                case 4:
                                    documentResponse = _context.sent;
                                    setDocument(documentResponse);
                                    // Check if user can edit this document
                                    if (!((user === null || user === void 0 ? void 0 : user.role) !== 'admin' && (user === null || user === void 0 ? void 0 : user.role) !== 'editor' && documentResponse.created_by_id !== (user === null || user === void 0 ? void 0 : user.id))) {
                                        _context.next = 9;
                                        break;
                                    }
                                    router.push("/documents/".concat(documentId));
                                    return _context.abrupt("return");
                                case 9:
                                    // Populate form with document data
                                    setFormData({
                                        title: documentResponse.title || '',
                                        "abstract": documentResponse["abstract"] || '',
                                        content: documentResponse.content || '',
                                        type: documentResponse.type || 'rule',
                                        agency_id: ((_documentResponse$age = documentResponse.agency_id) === null || _documentResponse$age === void 0 ? void 0 : _documentResponse$age.toString()) || '',
                                        category_ids: ((_documentResponse$cat = documentResponse.categories) === null || _documentResponse$cat === void 0 ? void 0 : _documentResponse$cat.map({
                                            "EditDocumentPage.useEffect.fetchData._ref._callee._callee$": function(cat) {
                                                return cat.id.toString();
                                            }
                                        }["EditDocumentPage.useEffect.fetchData._ref._callee._callee$"])) || [],
                                        tag_ids: ((_documentResponse$tag = documentResponse.tags) === null || _documentResponse$tag === void 0 ? void 0 : _documentResponse$tag.map({
                                            "EditDocumentPage.useEffect.fetchData._ref._callee._callee$": function(tag) {
                                                return tag.id.toString();
                                            }
                                        }["EditDocumentPage.useEffect.fetchData._ref._callee._callee$"])) || [],
                                        subject_ids: ((_documentResponse$sub = documentResponse.subjects) === null || _documentResponse$sub === void 0 ? void 0 : _documentResponse$sub.map({
                                            "EditDocumentPage.useEffect.fetchData._ref._callee._callee$": function(subj) {
                                                return subj.id.toString();
                                            }
                                        }["EditDocumentPage.useEffect.fetchData._ref._callee._callee$"])) || [],
                                        publication_date: documentResponse.publication_date ? documentResponse.publication_date.split('T')[0] : '',
                                        effective_date: documentResponse.effective_date ? documentResponse.effective_date.split('T')[0] : '',
                                        termination_date: documentResponse.termination_date ? documentResponse.termination_date.split('T')[0] : '',
                                        comment_due_date: documentResponse.comment_due_date ? documentResponse.comment_due_date.split('T')[0] : '',
                                        fr_document_number: documentResponse.fr_document_number || '',
                                        fr_citation: documentResponse.fr_citation || '',
                                        cfr_citations: documentResponse.cfr_citations || '',
                                        docket_number: documentResponse.docket_number || '',
                                        regulatory_identifier: documentResponse.regulatory_identifier || '',
                                        accepts_comments: documentResponse.accepts_comments || false,
                                        comment_instructions: documentResponse.comment_instructions || '',
                                        public_hearing_date: documentResponse.public_hearing_date ? documentResponse.public_hearing_date.split('T')[0] : '',
                                        public_hearing_info: documentResponse.public_hearing_info || '',
                                        page_count: documentResponse.page_count || 0,
                                        word_count: documentResponse.word_count || 0,
                                        language: documentResponse.language || 'en',
                                        original_format: documentResponse.original_format || 'pdf',
                                        file_size: documentResponse.file_size || 0,
                                        checksum: documentResponse.checksum || '',
                                        visibility_level: documentResponse.visibility_level || 1,
                                        is_public: documentResponse.is_public !== undefined ? documentResponse.is_public : true,
                                        significant_rule: documentResponse.significant_rule || false,
                                        economic_impact: documentResponse.economic_impact || '',
                                        small_entity_impact: documentResponse.small_entity_impact || false
                                    });
                                    // Fetch agencies
                                    _context.next = 12;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_9__["default"].getAgencies({
                                        per_page: 100
                                    });
                                case 12:
                                    agenciesResponse = _context.sent;
                                    setAgencies(agenciesResponse.data);
                                    // Fetch categories
                                    _context.next = 16;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_9__["default"].getCategories();
                                case 16:
                                    categoriesResponse = _context.sent;
                                    setCategories(categoriesResponse.data);
                                    // Fetch tags
                                    _context.next = 20;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_9__["default"].getTags();
                                case 20:
                                    tagsResponse = _context.sent;
                                    setTags(tagsResponse.data);
                                    // Fetch subjects
                                    _context.next = 24;
                                    return _services_api__WEBPACK_IMPORTED_MODULE_9__["default"].getSubjects();
                                case 24:
                                    subjectsResponse = _context.sent;
                                    setSubjects(subjectsResponse.data);
                                    _context.next = 31;
                                    break;
                                case 28:
                                    _context.prev = 28;
                                    _context.t0 = _context["catch"](0);
                                    setError(((_err$response = _context.t0.response) === null || _err$response === void 0 || (_err$response = _err$response.data) === null || _err$response === void 0 ? void 0 : _err$response.message) || 'Failed to fetch document data');
                                case 31:
                                    _context.prev = 31;
                                    setLoading(false);
                                    return _context.finish(31);
                                case 34:
                                case "end":
                                    return _context.stop();
                            }
                        }, _callee, null, [
                            [
                                0,
                                28,
                                31,
                                34
                            ]
                        ]);
                    }));
                    return function fetchData() {
                        return _ref.apply(this, arguments);
                    };
                }
            })["EditDocumentPage.useEffect.fetchData"]();
            if (documentId) {
                fetchData();
            }
        }
    }["EditDocumentPage.useEffect"], [
        documentId,
        isAuthenticated,
        user,
        router
    ]);
    var handleChange = function handleChange(e) {
        var _e$target = e.target, name = _e$target.name, value = _e$target.value, type = _e$target.type;
        if (type === 'checkbox') {
            var checked = e.target.checked;
            setFormData(function(prev) {
                return _objectSpread(_objectSpread({}, prev), {}, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])({}, name, checked));
            });
        } else if (type === 'number') {
            setFormData(function(prev) {
                return _objectSpread(_objectSpread({}, prev), {}, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])({}, name, parseInt(value) || 0));
            });
        } else {
            setFormData(function(prev) {
                return _objectSpread(_objectSpread({}, prev), {}, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])({}, name, value));
            });
        }
    };
    var handleCategoryChange = function handleCategoryChange(categoryId) {
        setFormData(function(prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
                category_ids: prev.category_ids.includes(categoryId) ? prev.category_ids.filter(function(id) {
                    return id !== categoryId;
                }) : [].concat((0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(prev.category_ids), [
                    categoryId
                ])
            });
        });
    };
    var handleTagChange = function handleTagChange(tagId) {
        setFormData(function(prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
                tag_ids: prev.tag_ids.includes(tagId) ? prev.tag_ids.filter(function(id) {
                    return id !== tagId;
                }) : [].concat((0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(prev.tag_ids), [
                    tagId
                ])
            });
        });
    };
    var handleSubjectChange = function handleSubjectChange(subjectId) {
        setFormData(function(prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
                subject_ids: prev.subject_ids.includes(subjectId) ? prev.subject_ids.filter(function(id) {
                    return id !== subjectId;
                }) : [].concat((0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(prev.subject_ids), [
                    subjectId
                ])
            });
        });
    };
    var handleSubmit = /*#__PURE__*/ function() {
        var _ref2 = (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__["default"])(/*#__PURE__*/ C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2(e) {
            var updateData, _err$response2;
            return C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {
                while(1)switch(_context2.prev = _context2.next){
                    case 0:
                        e.preventDefault();
                        if (documentId) {
                            _context2.next = 4;
                            break;
                        }
                        setError('Document ID is missing');
                        return _context2.abrupt("return");
                    case 4:
                        _context2.prev = 4;
                        setSaving(true);
                        setError('');
                        setSuccess('');
                        updateData = _objectSpread(_objectSpread({}, formData), {}, {
                            agency_id: formData.agency_id ? parseInt(formData.agency_id) : undefined,
                            category_ids: formData.category_ids.map(function(id) {
                                return parseInt(id);
                            }).filter(function(id) {
                                return !isNaN(id);
                            }),
                            tag_ids: formData.tag_ids.map(function(id) {
                                return parseInt(id);
                            }).filter(function(id) {
                                return !isNaN(id);
                            }),
                            subject_ids: formData.subject_ids.map(function(id) {
                                return parseInt(id);
                            }).filter(function(id) {
                                return !isNaN(id);
                            }),
                            cfr_citations: formData.cfr_citations ? formData.cfr_citations.split(',').map(function(c) {
                                return c.trim();
                            }).filter(function(c) {
                                return c;
                            }) : [],
                            publication_date: formData.publication_date || undefined,
                            effective_date: formData.effective_date || undefined,
                            termination_date: formData.termination_date || undefined,
                            comment_due_date: formData.comment_due_date || undefined,
                            public_hearing_date: formData.public_hearing_date || undefined
                        });
                        _context2.next = 11;
                        return _services_api__WEBPACK_IMPORTED_MODULE_9__["default"].updateDocument(parseInt(documentId), updateData);
                    case 11:
                        setSuccess('Document updated successfully!');
                        // Redirect to document detail page after a short delay
                        setTimeout(function() {
                            router.push("/documents/".concat(documentId));
                        }, 1500);
                        _context2.next = 18;
                        break;
                    case 15:
                        _context2.prev = 15;
                        _context2.t0 = _context2["catch"](4);
                        setError(((_err$response2 = _context2.t0.response) === null || _err$response2 === void 0 || (_err$response2 = _err$response2.data) === null || _err$response2 === void 0 ? void 0 : _err$response2.message) || 'Failed to update document. Please try again.');
                    case 18:
                        _context2.prev = 18;
                        setSaving(false);
                        return _context2.finish(18);
                    case 21:
                    case "end":
                        return _context2.stop();
                }
            }, _callee2, null, [
                [
                    4,
                    15,
                    18,
                    21
                ]
            ]);
        }));
        return function handleSubmit(_x) {
            return _ref2.apply(this, arguments);
        };
    }();
    if (!isAuthenticated || (user === null || user === void 0 ? void 0 : user.role) === 'viewer') {
        return null;
    }
    if (loading) {
        return __jsx(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_7__["default"], {
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 271,
                columnNumber: 7
            }
        }, __jsx("div", {
            className: "container-custom py-8",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 272,
                columnNumber: 9
            }
        }, __jsx("div", {
            className: "animate-pulse",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 273,
                columnNumber: 11
            }
        }, __jsx("div", {
            className: "h-8 bg-gray-200 rounded mb-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 274,
                columnNumber: 13
            }
        }), __jsx("div", {
            className: "h-4 bg-gray-200 rounded mb-8 w-1/2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 275,
                columnNumber: 13
            }
        }), __jsx("div", {
            className: "bg-white rounded-lg shadow-md p-6",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 276,
                columnNumber: 13
            }
        }, __jsx("div", {
            className: "space-y-6",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 277,
                columnNumber: 15
            }
        }, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(Array(8)).map(function(_, i) {
            return __jsx("div", {
                key: i,
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 279,
                    columnNumber: 19
                }
            }, __jsx("div", {
                className: "h-4 bg-gray-200 rounded mb-2 w-1/4",
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 280,
                    columnNumber: 21
                }
            }), __jsx("div", {
                className: "h-10 bg-gray-200 rounded",
                __self: _this,
                __source: {
                    fileName: _jsxFileName,
                    lineNumber: 281,
                    columnNumber: 21
                }
            }));
        }))))));
    }
    return __jsx(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_7__["default"], {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 293,
            columnNumber: 5
        }
    }, __jsx("div", {
        className: "container-custom py-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 294,
            columnNumber: 7
        }
    }, __jsx("div", {
        className: "mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 296,
            columnNumber: 9
        }
    }, __jsx((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {
        href: "/documents/".concat(documentId),
        className: "inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 297,
            columnNumber: 11
        }
    }, __jsx(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__["default"], {
        className: "h-4 w-4 mr-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 301,
            columnNumber: 13
        }
    }), "Back to Document"), __jsx("h1", {
        className: "text-3xl font-bold text-gray-900 mb-2 flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 304,
            columnNumber: 11
        }
    }, __jsx(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__["default"], {
        className: "h-8 w-8 mr-3 text-primary-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 305,
            columnNumber: 13
        }
    }), "Edit Document"), __jsx("p", {
        className: "text-gray-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 308,
            columnNumber: 11
        }
    }, "Update the document information and content")), error && __jsx("div", {
        className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6 flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 315,
            columnNumber: 11
        }
    }, __jsx(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__["default"], {
        className: "h-5 w-5 mr-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 316,
            columnNumber: 13
        }
    }), error), success && __jsx("div", {
        className: "bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-6 flex items-center",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 321,
            columnNumber: 11
        }
    }, __jsx(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__["default"], {
        className: "h-5 w-5 mr-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 322,
            columnNumber: 13
        }
    }), success), __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 328,
            columnNumber: 9
        }
    }, __jsx("form", {
        onSubmit: handleSubmit,
        className: "space-y-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 329,
            columnNumber: 11
        }
    }, __jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 331,
            columnNumber: 13
        }
    }, __jsx("div", {
        className: "md:col-span-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 332,
            columnNumber: 15
        }
    }, __jsx("label", {
        htmlFor: "title",
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 333,
            columnNumber: 17
        }
    }, "Document Title *"), __jsx("input", {
        type: "text",
        id: "title",
        name: "title",
        required: true,
        value: formData.title,
        onChange: handleChange,
        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
        placeholder: "Enter document title",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 336,
            columnNumber: 17
        }
    })), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 348,
            columnNumber: 15
        }
    }, __jsx("label", {
        htmlFor: "type",
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 349,
            columnNumber: 17
        }
    }, "Document Type *"), __jsx("select", {
        id: "type",
        name: "type",
        required: true,
        value: formData.type,
        onChange: handleChange,
        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 352,
            columnNumber: 17
        }
    }, __jsx("option", {
        value: "rule",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 360,
            columnNumber: 19
        }
    }, "Rule"), __jsx("option", {
        value: "proposed_rule",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 361,
            columnNumber: 19
        }
    }, "Proposed Rule"), __jsx("option", {
        value: "notice",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 362,
            columnNumber: 19
        }
    }, "Notice"), __jsx("option", {
        value: "presidential_document",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 363,
            columnNumber: 19
        }
    }, "Presidential Document"))), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 367,
            columnNumber: 15
        }
    }, __jsx("label", {
        htmlFor: "agency_id",
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 368,
            columnNumber: 17
        }
    }, "Agency *"), __jsx("select", {
        id: "agency_id",
        name: "agency_id",
        required: true,
        value: formData.agency_id,
        onChange: handleChange,
        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 371,
            columnNumber: 17
        }
    }, __jsx("option", {
        value: "",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 379,
            columnNumber: 19
        }
    }, "Select an agency"), agencies.map(function(agency) {
        return __jsx("option", {
            key: agency.id,
            value: agency.id,
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 381,
                columnNumber: 21
            }
        }, agency.name, " (", agency.short_name, ")");
    }))), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 388,
            columnNumber: 15
        }
    }, __jsx("label", {
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 389,
            columnNumber: 17
        }
    }, "Categories"), __jsx("div", {
        className: "space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 392,
            columnNumber: 17
        }
    }, categories.map(function(category) {
        return __jsx("label", {
            key: category.id,
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 394,
                columnNumber: 21
            }
        }, __jsx("input", {
            type: "checkbox",
            checked: formData.category_ids.includes(category.id.toString()),
            onChange: function onChange() {
                return handleCategoryChange(category.id.toString());
            },
            className: "mr-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 395,
                columnNumber: 23
            }
        }), category.name);
    }))), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 407,
            columnNumber: 15
        }
    }, __jsx("label", {
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 408,
            columnNumber: 17
        }
    }, "Tags"), __jsx("div", {
        className: "space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 411,
            columnNumber: 17
        }
    }, tags.map(function(tag) {
        return __jsx("label", {
            key: tag.id,
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 413,
                columnNumber: 21
            }
        }, __jsx("input", {
            type: "checkbox",
            checked: formData.tag_ids.includes(tag.id.toString()),
            onChange: function onChange() {
                return handleTagChange(tag.id.toString());
            },
            className: "mr-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 414,
                columnNumber: 23
            }
        }), tag.name);
    }))), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 426,
            columnNumber: 15
        }
    }, __jsx("label", {
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 427,
            columnNumber: 17
        }
    }, "Subjects"), __jsx("div", {
        className: "space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 430,
            columnNumber: 17
        }
    }, subjects.map(function(subject) {
        return __jsx("label", {
            key: subject.id,
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 432,
                columnNumber: 21
            }
        }, __jsx("input", {
            type: "checkbox",
            checked: formData.subject_ids.includes(subject.id.toString()),
            onChange: function onChange() {
                return handleSubjectChange(subject.id.toString());
            },
            className: "mr-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 433,
                columnNumber: 23
            }
        }), subject.name);
    })))), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 447,
            columnNumber: 13
        }
    }, __jsx("label", {
        htmlFor: "abstract",
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 448,
            columnNumber: 15
        }
    }, "Abstract"), __jsx("textarea", {
        id: "abstract",
        name: "abstract",
        rows: 3,
        value: formData["abstract"],
        onChange: handleChange,
        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
        placeholder: "Brief abstract of the document...",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 451,
            columnNumber: 15
        }
    })), __jsx("div", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 463,
            columnNumber: 13
        }
    }, __jsx("label", {
        htmlFor: "content",
        className: "block text-sm font-medium text-gray-700 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 464,
            columnNumber: 15
        }
    }, "Content *"), __jsx("textarea", {
        id: "content",
        name: "content",
        rows: 12,
        required: true,
        value: formData.content,
        onChange: handleChange,
        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
        placeholder: "Full document content (supports Markdown)...",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 467,
            columnNumber: 15
        }
    }), __jsx("p", {
        className: "mt-1 text-sm text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 477,
            columnNumber: 15
        }
    }, "You can use Markdown formatting for rich text content.")), __jsx("div", {
        className: "flex justify-end space-x-4 pt-6 border-t border-gray-200",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 483,
            columnNumber: 13
        }
    }, __jsx((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {
        href: "/documents/".concat(documentId),
        className: "px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 484,
            columnNumber: 15
        }
    }, "Cancel"), __jsx("button", {
        type: "submit",
        disabled: saving,
        className: "px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 490,
            columnNumber: 15
        }
    }, saving ? 'Updating...' : 'Update Document'))))));
};
_s(EditDocumentPage, "0GNvmUW88CXJc8LaoiyUOMEVrLg=", false, function() {
    return [
        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useParams,
        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,
        _stores_authStore__WEBPACK_IMPORTED_MODULE_8__.useAuthStore,
        _stores_authStore__WEBPACK_IMPORTED_MODULE_8__.useAuthStore,
        _stores_authStore__WEBPACK_IMPORTED_MODULE_8__.useAuthStore
    ];
});
_c = EditDocumentPage;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditDocumentPage);
var _c;
$RefreshReg$(_c, "EditDocumentPage");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});