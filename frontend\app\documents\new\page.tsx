'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XMarkIcon,
  DocumentTextIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  TagIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import { DocumentFormData } from '../../types';

const NewDocumentPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [agencies, setAgencies] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [formData, setFormData] = useState<DocumentFormData>({
    // Basic document information
    title: '',
    slug: '',
    abstract: '',
    content: '',
    type: 'rule',
    status: 'draft',

    // Federal Register specific fields
    fr_document_number: '',
    fr_citation: '',
    cfr_citations: '',
    publication_date: '',
    effective_date: '',
    termination_date: '',
    comment_due_date: '',

    // Document metadata
    page_count: 0,
    word_count: 0,
    language: 'en',
    original_format: '',
    file_size: 0,
    checksum: '',

    // Relationships
    agency_id: 0,
    created_by_id: user?.id || 0,
    parent_document_id: undefined,

    // Categories, tags, and subjects
    category_ids: [],
    tag_ids: [],
    subject_ids: [],

    // Regulatory information
    regulatory_identifier: '',
    docket_number: '',
    significant_rule: false,
    economic_impact: '',
    small_entity_impact: false,

    // Public participation
    accepts_comments: true,
    comment_count: 0,
    comment_instructions: '',
    public_hearing_date: '',
    public_hearing_info: '',

    // Document visibility and access control
    visibility_level: 1,
    is_public: true,

    // Search and indexing
    view_count: 0,
    download_count: 0,

    // OCR and digital signature fields
    ocr_text: '',
    ocr_confidence: 0,
    ocr_processed: false,
    require_signature: false,
    signature_method: 'digital',
  });
  const [tagInput, setTagInput] = useState('');
  const [tags, setTags] = useState<any[]>([]);

  useEffect(() => {
    if (!isAuthenticated || user?.role === 'viewer') {
      router.push('/dashboard');
      return;
    }

    const fetchData = async () => {
      try {
        // Fetch agencies
        const agenciesResponse = await apiService.getAgencies({ per_page: 100 });
        setAgencies(agenciesResponse.data);

        // Fetch categories
        const categoriesResponse = await apiService.getCategories();
        setCategories(categoriesResponse.data);

        // Fetch tags
        const tagsResponse = await apiService.getTags();
        setTags(tagsResponse.data);

        // Load default values for new document
        await loadDefaultValues();
      } catch (err) {
        console.error('Error fetching data:', err);
      }
    };

    fetchData();
  }, [isAuthenticated, user, router]);

  // Load default values from preloading API
  const loadDefaultValues = async () => {
    try {
      const response = await apiService.getDocumentDefaults();
      const defaults = response.data;

      // Format dates for input fields (YYYY-MM-DD)
      const formatDate = (dateString: string) => {
        if (!dateString) return '';
        return new Date(dateString).toISOString().split('T')[0];
      };

      setFormData(prev => ({
        ...prev,
        publication_date: formatDate(defaults.publication_date),
        effective_date: formatDate(defaults.effective_date),
        comment_due_date: formatDate(defaults.comment_due_date),
        fr_document_number: defaults.fr_document_number || '',
        docket_number: defaults.docket_number || '',
        language: defaults.language || 'en',
        original_format: defaults.original_format || 'pdf',
        status: defaults.status || 'draft',
        type: defaults.type || 'rule',
        visibility_level: defaults.visibility_level || 1,
        is_public: defaults.is_public !== undefined ? defaults.is_public : true,
        accepts_comments: defaults.accepts_comments !== undefined ? defaults.accepts_comments : true,
      }));
    } catch (err) {
      console.error('Error loading default values:', err);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Regenerate docket number when agency changes
      if (name === 'agency_id' && value) {
        regenerateDocketNumber(parseInt(value), formData.category_ids);
      }

      // Smart date calculations when publication date changes
      if (name === 'publication_date' && value) {
        calculateRelatedDates(value);
      }
    }
  };

  // Calculate related dates based on publication date
  const calculateRelatedDates = (publicationDate: string) => {
    if (!publicationDate) return;

    const pubDate = new Date(publicationDate);

    // Calculate effective date (30 days after publication)
    const effectiveDate = new Date(pubDate);
    effectiveDate.setDate(effectiveDate.getDate() + 30);

    // Calculate comment due date (60 days after publication)
    const commentDueDate = new Date(pubDate);
    commentDueDate.setDate(commentDueDate.getDate() + 60);

    // Format dates for input fields (YYYY-MM-DD)
    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    setFormData(prev => ({
      ...prev,
      // Only update if the fields are empty (don't override user input)
      effective_date: prev.effective_date || formatDate(effectiveDate),
      comment_due_date: prev.comment_due_date || formatDate(commentDueDate),
    }));
  };

  // Regenerate docket number based on agency and categories
  const regenerateDocketNumber = async (agencyId: number, categoryIds: number[]) => {
    try {
      const params = new URLSearchParams({
        agency_id: agencyId.toString(),
      });

      if (categoryIds.length > 0) {
        params.append('category_ids', categoryIds.join(','));
      }

      const response = await fetch(`/api/v1/preloading/docket-number?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({
          ...prev,
          docket_number: data.docket_number || ''
        }));
      }
    } catch (err) {
      console.error('Error regenerating docket number:', err);
    }
  };

  // Regenerate FR document number
  const regenerateFRNumber = async () => {
    try {
      const response = await apiService.generateFRNumber();
      const data = response.data;
      setFormData(prev => ({
        ...prev,
        fr_document_number: data.fr_document_number || ''
      }));
    } catch (err) {
      console.error('Error regenerating FR number:', err);
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    const numericCategoryId = parseInt(categoryId);
    const newCategoryIds = formData.category_ids.includes(numericCategoryId)
      ? formData.category_ids.filter(id => id !== numericCategoryId)
      : [...formData.category_ids, numericCategoryId];

    setFormData(prev => ({
      ...prev,
      category_ids: newCategoryIds
    }));

    // Regenerate docket number if agency is selected
    if (formData.agency_id) {
      regenerateDocketNumber(formData.agency_id, newCategoryIds);
    }
  };

  const handleTagChange = (tagId: string) => {
    const numericTagId = parseInt(tagId);
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids.includes(numericTagId)
        ? prev.tag_ids.filter(id => id !== numericTagId)
        : [...prev.tag_ids, numericTagId]
    }));
  };

  // Remove old tag functions since we're using tag_ids now

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const documentData = {
        ...formData,
        agency_id: formData.agency_id || undefined,
        category_ids: formData.category_ids.filter(id => id > 0),
        tag_ids: formData.tag_ids.filter(id => id > 0),
        subject_ids: formData.subject_ids.filter(id => id > 0),
        cfr_citations: formData.cfr_citations ? formData.cfr_citations.split(',').map(c => c.trim()).filter(c => c) : [],
        publication_date: formData.publication_date || undefined,
        effective_date: formData.effective_date || undefined,
        termination_date: formData.termination_date || undefined,
        comment_due_date: formData.comment_due_date || undefined,
        public_hearing_date: formData.public_hearing_date || undefined,
      };

      const response = await apiService.createDocument(documentData);
      setSuccess('Document created successfully!');

      // Redirect to document detail page after a short delay
      setTimeout(() => {
        router.push(`/documents/${response.data.id}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create document. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated || user?.role === 'viewer') {
    return null;
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/documents"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Documents
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Document</h1>
          <p className="text-gray-600">
            Create a new federal document or regulation
          </p>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6 flex items-center">
            <XMarkIcon className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-6 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            {success}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter document title"
                />
              </div>

              <div>
                <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Slug
                </label>
                <input
                  type="text"
                  id="slug"
                  name="slug"
                  value={formData.slug}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="URL-friendly identifier (auto-generated if empty)"
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Type *
                </label>
                <select
                  id="type"
                  name="type"
                  required
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="rule">Rule</option>
                  <option value="proposed_rule">Proposed Rule</option>
                  <option value="notice">Notice</option>
                  <option value="presidential_document">Presidential Document</option>
                </select>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="draft">Draft</option>
                  <option value="review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div>
                <label htmlFor="agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency *
                </label>
                <select
                  id="agency_id"
                  name="agency_id"
                  required
                  value={formData.agency_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select an agency</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.name} ({agency.short_name})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categories
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                  {categories.map((category) => (
                    <label key={category.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.category_ids.includes(category.id.toString())}
                        onChange={() => handleCategoryChange(category.id.toString())}
                        className="mr-2"
                      />
                      {category.name}
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Abstract */}
            <div>
              <label htmlFor="abstract" className="block text-sm font-medium text-gray-700 mb-2">
                Abstract
              </label>
              <textarea
                id="abstract"
                name="abstract"
                rows={3}
                value={formData.abstract}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief abstract of the document..."
              />
            </div>



            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Content *
              </label>
              <textarea
                id="content"
                name="content"
                rows={12}
                required
                value={formData.content}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Full document content (supports Markdown)..."
              />
              <p className="mt-1 text-sm text-gray-500">
                You can use Markdown formatting for rich text content.
              </p>
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label htmlFor="publication_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Publication Date
                </label>
                <input
                  type="date"
                  id="publication_date"
                  name="publication_date"
                  value={formData.publication_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="effective_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Effective Date
                </label>
                <input
                  type="date"
                  id="effective_date"
                  name="effective_date"
                  value={formData.effective_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="comment_due_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Comment Due Date
                </label>
                <input
                  type="date"
                  id="comment_due_date"
                  name="comment_due_date"
                  value={formData.comment_due_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="termination_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Termination Date
                </label>
                <input
                  type="date"
                  id="termination_date"
                  name="termination_date"
                  value={formData.termination_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            {/* Document Numbers */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label htmlFor="fr_document_number" className="block text-sm font-medium text-gray-700 mb-2">
                  FR Document Number
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    id="fr_document_number"
                    name="fr_document_number"
                    value={formData.fr_document_number}
                    onChange={handleChange}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Auto-generated if empty"
                  />
                  <button
                    type="button"
                    onClick={regenerateFRNumber}
                    className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    title="Regenerate FR Number"
                  >
                    🔄
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="fr_citation" className="block text-sm font-medium text-gray-700 mb-2">
                  FR Citation
                </label>
                <input
                  type="text"
                  id="fr_citation"
                  name="fr_citation"
                  value={formData.fr_citation}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., 90 FR 1234"
                />
              </div>

              <div>
                <label htmlFor="docket_number" className="block text-sm font-medium text-gray-700 mb-2">
                  Docket Number
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    id="docket_number"
                    name="docket_number"
                    value={formData.docket_number}
                    onChange={handleChange}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Auto-generated if empty"
                  />
                  <button
                    type="button"
                    onClick={() => formData.agency_id && regenerateDocketNumber(formData.agency_id, formData.category_ids)}
                    disabled={!formData.agency_id}
                    className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Regenerate Docket Number (requires agency selection)"
                  >
                    🔄
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="cfr_citations" className="block text-sm font-medium text-gray-700 mb-2">
                  CFR Citations
                </label>
                <input
                  type="text"
                  id="cfr_citations"
                  name="cfr_citations"
                  value={formData.cfr_citations}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., 40 CFR 52, 40 CFR 81"
                />
              </div>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                {tags.map((tag) => (
                  <label key={tag.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.tag_ids.includes(tag.id.toString())}
                      onChange={() => handleTagChange(tag.id.toString())}
                      className="mr-2"
                    />
                    {tag.name}
                  </label>
                ))}
              </div>
            </div>

            {/* Comments and Public Participation */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Comments and Public Participation</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="accepts_comments"
                    checked={formData.accepts_comments}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Accepts Public Comments</span>
                </label>

                <div>
                  <label htmlFor="public_hearing_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Public Hearing Date
                  </label>
                  <input
                    type="date"
                    id="public_hearing_date"
                    name="public_hearing_date"
                    value={formData.public_hearing_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="comment_instructions" className="block text-sm font-medium text-gray-700 mb-2">
                  Comment Instructions
                </label>
                <textarea
                  id="comment_instructions"
                  name="comment_instructions"
                  rows={3}
                  value={formData.comment_instructions}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Instructions for submitting comments..."
                />
              </div>

              <div>
                <label htmlFor="public_hearing_info" className="block text-sm font-medium text-gray-700 mb-2">
                  Public Hearing Information
                </label>
                <textarea
                  id="public_hearing_info"
                  name="public_hearing_info"
                  rows={3}
                  value={formData.public_hearing_info}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Information about public hearings..."
                />
              </div>
            </div>

            {/* Regulatory Significance */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Regulatory Significance</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="significant_rule"
                    checked={formData.significant_rule}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Significant Rule</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="small_entity_impact"
                    checked={formData.small_entity_impact}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Small Entity Impact</span>
                </label>

                <div className="md:col-span-2">
                  <label htmlFor="economic_impact" className="block text-sm font-medium text-gray-700 mb-2">
                    Economic Impact Description
                  </label>
                  <textarea
                    id="economic_impact"
                    name="economic_impact"
                    rows={3}
                    value={formData.economic_impact}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Describe the economic impact of this rule..."
                  />
                </div>

                <div>
                  <label htmlFor="regulatory_identifier" className="block text-sm font-medium text-gray-700 mb-2">
                    Regulatory Identifier (RIN)
                  </label>
                  <input
                    type="text"
                    id="regulatory_identifier"
                    name="regulatory_identifier"
                    value={formData.regulatory_identifier}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 2040-AF12"
                  />
                </div>
              </div>
            </div>

            {/* Document Metadata */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Document Metadata</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <label htmlFor="page_count" className="block text-sm font-medium text-gray-700 mb-2">
                    Page Count
                  </label>
                  <input
                    type="number"
                    id="page_count"
                    name="page_count"
                    min="0"
                    value={formData.page_count}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="word_count" className="block text-sm font-medium text-gray-700 mb-2">
                    Word Count
                  </label>
                  <input
                    type="number"
                    id="word_count"
                    name="word_count"
                    min="0"
                    value={formData.word_count}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
                    Language
                  </label>
                  <select
                    id="language"
                    name="language"
                    value={formData.language}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="zh">Chinese</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="original_format" className="block text-sm font-medium text-gray-700 mb-2">
                    Original Format
                  </label>
                  <select
                    id="original_format"
                    name="original_format"
                    value={formData.original_format}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="pdf">PDF</option>
                    <option value="doc">DOC</option>
                    <option value="docx">DOCX</option>
                    <option value="txt">TXT</option>
                    <option value="html">HTML</option>
                    <option value="xml">XML</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="visibility_level" className="block text-sm font-medium text-gray-700 mb-2">
                    Visibility Level
                  </label>
                  <select
                    id="visibility_level"
                    name="visibility_level"
                    value={formData.visibility_level}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value={1}>Public</option>
                    <option value={2}>Restricted</option>
                    <option value={3}>Confidential</option>
                  </select>
                </div>

                <div className="flex items-center pt-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="is_public"
                      checked={formData.is_public}
                      onChange={handleChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Make document publicly accessible</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Document Metadata */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Document Metadata</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="page_count" className="block text-sm font-medium text-gray-700 mb-2">
                    Page Count
                  </label>
                  <input
                    type="number"
                    id="page_count"
                    name="page_count"
                    min="0"
                    value={formData.page_count}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="word_count" className="block text-sm font-medium text-gray-700 mb-2">
                    Word Count
                  </label>
                  <input
                    type="number"
                    id="word_count"
                    name="word_count"
                    min="0"
                    value={formData.word_count}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="file_size" className="block text-sm font-medium text-gray-700 mb-2">
                    File Size (bytes)
                  </label>
                  <input
                    type="number"
                    id="file_size"
                    name="file_size"
                    min="0"
                    value={formData.file_size}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="checksum" className="block text-sm font-medium text-gray-700 mb-2">
                    File Checksum
                  </label>
                  <input
                    type="text"
                    id="checksum"
                    name="checksum"
                    value={formData.checksum}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="SHA-256 or MD5 hash"
                  />
                </div>

                <div>
                  <label htmlFor="view_count" className="block text-sm font-medium text-gray-700 mb-2">
                    View Count
                  </label>
                  <input
                    type="number"
                    id="view_count"
                    name="view_count"
                    min="0"
                    value={formData.view_count}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    readOnly
                  />
                </div>

                <div>
                  <label htmlFor="download_count" className="block text-sm font-medium text-gray-700 mb-2">
                    Download Count
                  </label>
                  <input
                    type="number"
                    id="download_count"
                    name="download_count"
                    min="0"
                    value={formData.download_count}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* OCR and Digital Signature */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">OCR and Digital Signature</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="ocr_text" className="block text-sm font-medium text-gray-700 mb-2">
                    OCR Text
                  </label>
                  <textarea
                    id="ocr_text"
                    name="ocr_text"
                    rows={4}
                    value={formData.ocr_text}
                    onChange={handleChange}
                    placeholder="Extracted text from OCR processing..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="ocr_confidence" className="block text-sm font-medium text-gray-700 mb-2">
                      OCR Confidence (%)
                    </label>
                    <input
                      type="number"
                      id="ocr_confidence"
                      name="ocr_confidence"
                      min="0"
                      max="100"
                      value={formData.ocr_confidence}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  <div className="flex items-center space-x-6">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        name="ocr_processed"
                        checked={formData.ocr_processed}
                        onChange={handleChange}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">OCR Processed</span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        name="require_signature"
                        checked={formData.require_signature}
                        onChange={handleChange}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Require Digital Signature</span>
                    </label>
                  </div>

                  {formData.require_signature && (
                    <div>
                      <label htmlFor="signature_method" className="block text-sm font-medium text-gray-700 mb-2">
                        Signature Method
                      </label>
                      <select
                        id="signature_method"
                        name="signature_method"
                        value={formData.signature_method}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="digital">Digital Signature</option>
                        <option value="electronic">Electronic Signature</option>
                        <option value="wet">Wet Signature</option>
                      </select>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href="/documents"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Document'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default NewDocumentPage;
