package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

const baseURL = "http://127.0.0.1:8080/api/v1"

type AuthResponse struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refresh_token"`
	User         struct {
		ID   int    `json:"id"`
		Role string `json:"role"`
	} `json:"user"`
}

func main() {
	// Authenticate first
	token, err := authenticate()
	if err != nil {
		fmt.Printf("❌ Authentication failed: %v\n", err)
		return
	}

	// Test document 150
	fmt.Printf("🔍 Testing document 150...\n")
	err = testDocument(token, 150)
	if err != nil {
		fmt.Printf("❌ Document 150 test failed: %v\n", err)
	} else {
		fmt.Printf("✅ Document 150 exists and is accessible\n")
	}

	// Test document 147
	fmt.Printf("\n🔍 Testing document 147...\n")
	err = testDocument(token, 147)
	if err != nil {
		fmt.Printf("❌ Document 147 test failed: %v\n", err)
	} else {
		fmt.Printf("✅ Document 147 exists and is accessible\n")
	}

	// Test updating document 147
	fmt.Printf("\n🔄 Testing document 147 update...\n")
	err = testDocumentUpdate(token, 147)
	if err != nil {
		fmt.Printf("❌ Document 147 update failed: %v\n", err)
	} else {
		fmt.Printf("✅ Document 147 update successful\n")
	}

	// List some documents to see what's available
	fmt.Printf("\n📋 Listing available documents...\n")
	err = listDocuments(token)
	if err != nil {
		fmt.Printf("❌ Failed to list documents: %v\n", err)
	}
}

func authenticate() (string, error) {
	loginData := map[string]string{
		"identifier": "<EMAIL>",
		"password":   "testpass123",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := http.Post(baseURL+"/auth/login", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	var authResp AuthResponse
	if err := json.Unmarshal(body, &authResp); err != nil {
		return "", err
	}

	return authResp.Token, nil
}

func testDocument(token string, id int) error {
	req, _ := http.NewRequest("GET", fmt.Sprintf("%s/documents/%d", baseURL, id), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		fmt.Printf("❌ Status: %d, Response: %s\n", resp.StatusCode, string(body))
		return fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	fmt.Printf("✅ Document %d found, Status: %d\n", id, resp.StatusCode)

	// Parse and show basic info
	var doc map[string]interface{}
	if err := json.Unmarshal(body, &doc); err == nil {
		if title, ok := doc["title"]; ok {
			fmt.Printf("   Title: %v\n", title)
		}
		if docType, ok := doc["type"]; ok {
			fmt.Printf("   Type: %v\n", docType)
		}
		if status, ok := doc["status"]; ok {
			fmt.Printf("   Status: %v\n", status)
		}
	}

	return nil
}

func testDocumentUpdate(token string, id int) error {
	updateData := map[string]interface{}{
		"title":    fmt.Sprintf("Updated Test Document %d", id),
		"abstract": "This document has been updated via API test",
	}

	jsonData, _ := json.Marshal(updateData)
	req, _ := http.NewRequest("PUT", fmt.Sprintf("%s/documents/%d", baseURL, id), strings.NewReader(string(jsonData)))
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		fmt.Printf("❌ Update Status: %d, Response: %s\n", resp.StatusCode, string(body))
		return fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	fmt.Printf("✅ Document %d updated successfully, Status: %d\n", id, resp.StatusCode)
	return nil
}

func listDocuments(token string) error {
	req, _ := http.NewRequest("GET", baseURL+"/documents?per_page=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		fmt.Printf("❌ Status: %d, Response: %s\n", resp.StatusCode, string(body))
		return fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return err
	}

	if data, ok := result["data"].([]interface{}); ok {
		fmt.Printf("📄 Found %d documents:\n", len(data))
		for i, doc := range data {
			if docMap, ok := doc.(map[string]interface{}); ok {
				id := docMap["id"]
				title := docMap["title"]
				fmt.Printf("   %d. ID: %v, Title: %v\n", i+1, id, title)
			}
		}
	}

	return nil
}
