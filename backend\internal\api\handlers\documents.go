package handlers

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// Document handlers

var (
	documentSummaryService *services.SummaryService
	fileStorageService     *services.FileStorageService
	globalConfig           *config.Config
)

// InitializeDocumentServices initializes document-related services
func InitializeDocumentServices(cfg *config.Config) {
	globalConfig = cfg
	fileStorageService = services.NewFileStorageService(cfg)
}

// getConfig returns the global config
func getConfig() *config.Config {
	return globalConfig
}

// GetDocuments returns all documents (authenticated endpoint)
func GetDocuments(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get pagination parameters
	pagination := GetPaginationParams(c)

	// Count total documents
	var total int64
	db.Model(&models.Document{}).Count(&total)

	// Get documents with pagination
	var documents []models.Document
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := db.Preload("CreatedBy").
		Preload("UpdatedBy").
		Preload("Categories").
		Preload("Tags").
		Offset(offset).
		Limit(pagination.PerPage).
		Order("created_at DESC").
		Find(&documents).Error; err != nil {
		HandleInternalError(c, "Failed to fetch documents: "+err.Error())
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, doc := range documents {
		documentResponses[i] = gin.H{
			"id":               doc.ID,
			"title":            doc.Title,
			"slug":             doc.Slug,
			"abstract":         doc.Abstract,
			"type":             doc.Type,
			"status":           doc.Status,
			"effective_date":   doc.EffectiveDate,
			"publication_date": doc.PublicationDate,
			"created_by":       doc.CreatedBy,
			"updated_by":       doc.UpdatedBy,
			"categories":       doc.Categories,
			"tags":             doc.Tags,
			"view_count":       doc.ViewCount,
			"created_at":       doc.CreatedAt,
			"updated_at":       doc.UpdatedAt,
		}
	}

	// Return paginated response
	response := CreatePaginationResponse(documentResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// generateDocumentSlug creates a URL-friendly slug from a string
func generateDocumentSlug(text string) string {
	// Convert to lowercase and replace spaces with hyphens
	slug := strings.ToLower(text)
	slug = strings.ReplaceAll(slug, " ", "-")
	// Remove special characters (keep only alphanumeric and hyphens)
	var result strings.Builder
	for _, r := range slug {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// GetPublicDocuments returns public documents
func GetPublicDocuments(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get pagination parameters
	pagination := GetPaginationParams(c)

	// Build query for public documents only
	query := db.Model(&models.Document{}).Where("is_public = ? AND status = ?", true, "published")

	// Apply search filter if provided
	if search := c.Query("search"); search != "" {
		query = query.Where("title ILIKE ? OR abstract ILIKE ? OR content ILIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply type filter if provided
	if docType := c.Query("type"); docType != "" {
		query = query.Where("type = ?", docType)
	}

	// Apply agency filter if provided
	if agencyID := c.Query("agency_id"); agencyID != "" {
		query = query.Where("agency_id = ?", agencyID)
	}

	// Apply date filters if provided
	if fromDate := c.Query("from_date"); fromDate != "" {
		if parsedDate, err := time.Parse("2006-01-02", fromDate); err == nil {
			query = query.Where("publication_date >= ?", parsedDate)
		}
	}

	if toDate := c.Query("to_date"); toDate != "" {
		if parsedDate, err := time.Parse("2006-01-02", toDate); err == nil {
			query = query.Where("publication_date <= ?", parsedDate)
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to count documents",
			Message: err.Error(),
		})
		return
	}

	// Get documents with pagination
	var documents []models.Document
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := query.Preload("Agency").
		Preload("CreatedBy").
		Preload("Categories").
		Preload("Tags").
		Order("publication_date DESC, created_at DESC").
		Offset(offset).
		Limit(pagination.PerPage).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch documents",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, document := range documents {
		response := gin.H{
			"id":                 document.ID,
			"title":              document.Title,
			"slug":               document.Slug,
			"abstract":           document.Abstract,
			"type":               document.Type,
			"status":             document.Status,
			"publication_date":   document.PublicationDate,
			"effective_date":     document.EffectiveDate,
			"termination_date":   document.TerminationDate,
			"fr_document_number": document.FRDocumentNumber,
			"docket_number":      document.DocketNumber,
			"accepts_comments":   document.AcceptsComments,
			"comment_due_date":   document.CommentDueDate,
			"view_count":         document.ViewCount,
			"download_count":     document.DownloadCount,
			"created_at":         document.CreatedAt,
			"updated_at":         document.UpdatedAt,
		}

		if document.Agency.ID != 0 {
			response["agency"] = gin.H{
				"id":         document.Agency.ID,
				"name":       document.Agency.Name,
				"short_name": document.Agency.ShortName,
				"slug":       document.Agency.Slug,
			}
		}

		if document.CreatedBy.ID != 0 {
			response["created_by"] = gin.H{
				"id":         document.CreatedBy.ID,
				"first_name": document.CreatedBy.FirstName,
				"last_name":  document.CreatedBy.LastName,
				"email":      document.CreatedBy.Email,
			}
		}

		// Add categories
		categories := make([]gin.H, len(document.Categories))
		for j, category := range document.Categories {
			categories[j] = gin.H{
				"id":   category.ID,
				"name": category.Name,
				"slug": category.Slug,
			}
		}
		response["categories"] = categories

		// Add tags
		tags := make([]gin.H, len(document.Tags))
		for j, tag := range document.Tags {
			tags[j] = gin.H{
				"id":   tag.ID,
				"name": tag.Name,
				"slug": tag.Slug,
			}
		}
		response["tags"] = tags

		documentResponses[i] = response
	}

	// Create paginated response
	response := CreatePaginationResponse(documentResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// CreateDocument creates a new document with comprehensive validation
func CreateDocument(c *gin.Context) {
	var req struct {
		Title                string   `json:"title" binding:"required"`
		Abstract             string   `json:"abstract"`
		Content              string   `json:"content"`
		Type                 string   `json:"type" binding:"required"`
		AgencyID             uint     `json:"agency_id" binding:"required"`
		CategoryIDs          []uint   `json:"category_ids"`
		TagIDs               []uint   `json:"tag_ids"`
		SubjectIDs           []uint   `json:"subject_ids"`
		PublicationDate      string   `json:"publication_date"`
		EffectiveDate        string   `json:"effective_date"`
		TerminationDate      string   `json:"termination_date"`
		CommentDueDate       string   `json:"comment_due_date"`
		FRDocumentNumber     string   `json:"fr_document_number"`
		FRCitation           string   `json:"fr_citation"`
		CFRCitations         []string `json:"cfr_citations"`
		DocketNumber         string   `json:"docket_number"`
		RegulatoryIdentifier string   `json:"regulatory_identifier"`
		AcceptsComments      bool     `json:"accepts_comments"`
		CommentInstructions  string   `json:"comment_instructions"`
		PublicHearingDate    string   `json:"public_hearing_date"`
		PublicHearingInfo    string   `json:"public_hearing_info"`
		SignificantRule      bool     `json:"significant_rule"`
		EconomicImpact       string   `json:"economic_impact"`
		SmallEntityImpact    bool     `json:"small_entity_impact"`
		PageCount            int      `json:"page_count"`
		WordCount            int      `json:"word_count"`
		Language             string   `json:"language"`
		OriginalFormat       string   `json:"original_format"`
		FileSize             int64    `json:"file_size"`
		Checksum             string   `json:"checksum"`
		VisibilityLevel      int      `json:"visibility_level"`
		IsPublic             bool     `json:"is_public"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Validate agency exists
	var agency models.Agency
	if err := db.First(&agency, req.AgencyID).Error; err != nil {
		HandleBadRequest(c, "Invalid agency ID")
		return
	}

	// Create document
	document := models.Document{
		Title:                req.Title,
		Abstract:             req.Abstract,
		Content:              req.Content,
		Type:                 models.DocumentType(req.Type),
		Status:               models.StatusDraft,
		AgencyID:             req.AgencyID,
		CreatedByID:          userID.(uint),
		FRDocumentNumber:     req.FRDocumentNumber,
		FRCitation:           req.FRCitation,
		CFRCitations:         strings.Join(req.CFRCitations, ","),
		DocketNumber:         req.DocketNumber,
		RegulatoryIdentifier: req.RegulatoryIdentifier,
		AcceptsComments:      req.AcceptsComments,
		CommentInstructions:  req.CommentInstructions,
		PublicHearingInfo:    req.PublicHearingInfo,
		SignificantRule:      req.SignificantRule,
		EconomicImpact:       req.EconomicImpact,
		SmallEntityImpact:    req.SmallEntityImpact,
		PageCount:            req.PageCount,
		WordCount:            req.WordCount,
		Language:             req.Language,
		OriginalFormat:       req.OriginalFormat,
		FileSize:             req.FileSize,
		Checksum:             req.Checksum,
		VisibilityLevel:      req.VisibilityLevel,
		IsPublic:             req.IsPublic,
	}

	// Generate slug from title
	document.Slug = generateDocumentSlug(req.Title)

	// Parse dates if provided
	if req.PublicationDate != "" {
		if pubDate, err := time.Parse("2006-01-02", req.PublicationDate); err == nil {
			document.PublicationDate = &pubDate
		}
	}

	if req.EffectiveDate != "" {
		if effDate, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			document.EffectiveDate = &effDate
		}
	}

	if req.TerminationDate != "" {
		if termDate, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			document.TerminationDate = &termDate
		}
	}

	if req.CommentDueDate != "" {
		if commentDate, err := time.Parse("2006-01-02", req.CommentDueDate); err == nil {
			document.CommentDueDate = &commentDate
		}
	}

	if req.PublicHearingDate != "" {
		if hearingDate, err := time.Parse("2006-01-02T15:04:05Z", req.PublicHearingDate); err == nil {
			document.PublicHearingDate = &hearingDate
		}
	}

	// Start transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create document
	if err := tx.Create(&document).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Document creation failed",
			Message: err.Error(),
		})
		return
	}

	// Associate categories
	if len(req.CategoryIDs) > 0 {
		var categories []models.Category
		if err := tx.Where("id IN ?", req.CategoryIDs).Find(&categories).Error; err == nil {
			if err := tx.Model(&document).Association("Categories").Append(categories); err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "Failed to associate categories",
					Message: err.Error(),
				})
				return
			}
		}
	}

	// Associate tags
	if len(req.TagIDs) > 0 {
		var tags []models.Tag
		if err := tx.Where("id IN ?", req.TagIDs).Find(&tags).Error; err == nil {
			if err := tx.Model(&document).Association("Tags").Append(tags); err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "Failed to associate tags",
					Message: err.Error(),
				})
				return
			}
		}
	}

	// Associate subjects
	if len(req.SubjectIDs) > 0 {
		var subjects []models.Subject
		if err := tx.Where("id IN ?", req.SubjectIDs).Find(&subjects).Error; err == nil {
			if err := tx.Model(&document).Association("Subjects").Append(subjects); err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "Failed to associate subjects",
					Message: err.Error(),
				})
				return
			}
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to commit transaction",
			Message: err.Error(),
		})
		return
	}

	// Create summary for document creation
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.CreateDocumentSummary(&document, models.ActionTypeCreate, userID.(uint)); err != nil {
		// Log the error but don't fail the document creation
		fmt.Printf("Warning: Failed to create summary for document %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Document created successfully",
		Data:    gin.H{"id": document.ID, "title": req.Title, "slug": document.Slug},
	})
}

// GetDocument returns a specific document by ID
func GetDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find document with all related data
	var document models.Document
	if err := db.Preload("Agency").
		Preload("CreatedBy").
		Preload("UpdatedBy").
		Preload("Categories").
		Preload("Tags").
		Preload("Subjects").
		Preload("Files").
		Preload("Comments", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Moderator").Order("created_at DESC").Limit(10)
		}).
		Preload("Reviews", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Reviewer").Order("created_at DESC")
		}).
		First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Build comprehensive response
	response := gin.H{
		"id":                    document.ID,
		"title":                 document.Title,
		"slug":                  document.Slug,
		"abstract":              document.Abstract,
		"content":               document.Content,
		"type":                  document.Type,
		"status":                document.Status,
		"publication_date":      document.PublicationDate,
		"effective_date":        document.EffectiveDate,
		"termination_date":      document.TerminationDate,
		"comment_due_date":      document.CommentDueDate,
		"fr_document_number":    document.FRDocumentNumber,
		"fr_citation":           document.FRCitation,
		"cfr_citations":         document.CFRCitations,
		"docket_number":         document.DocketNumber,
		"regulatory_identifier": document.RegulatoryIdentifier,
		"accepts_comments":      document.AcceptsComments,
		"comment_instructions":  document.CommentInstructions,
		"public_hearing_date":   document.PublicHearingDate,
		"public_hearing_info":   document.PublicHearingInfo,
		"significant_rule":      document.SignificantRule,
		"economic_impact":       document.EconomicImpact,
		"small_entity_impact":   document.SmallEntityImpact,
		"page_count":            document.PageCount,
		"word_count":            document.WordCount,
		"language":              document.Language,
		"original_format":       document.OriginalFormat,
		"file_size":             document.FileSize,
		"checksum":              document.Checksum,
		"visibility_level":      document.VisibilityLevel,
		"is_public":             document.IsPublic,
		"view_count":            document.ViewCount,
		"download_count":        document.DownloadCount,
		"comment_count":         document.CommentCount,
		"created_at":            document.CreatedAt,
		"updated_at":            document.UpdatedAt,
	}

	// Add agency information
	if document.Agency.ID != 0 {
		response["agency"] = gin.H{
			"id":         document.Agency.ID,
			"name":       document.Agency.Name,
			"short_name": document.Agency.ShortName,
			"slug":       document.Agency.Slug,
		}
	}

	// Add creator information
	if document.CreatedBy.ID != 0 {
		response["created_by"] = gin.H{
			"id":         document.CreatedBy.ID,
			"first_name": document.CreatedBy.FirstName,
			"last_name":  document.CreatedBy.LastName,
			"email":      document.CreatedBy.Email,
		}
	}

	// Add updater information
	if document.UpdatedBy != nil && document.UpdatedBy.ID != 0 {
		response["updated_by"] = gin.H{
			"id":         document.UpdatedBy.ID,
			"first_name": document.UpdatedBy.FirstName,
			"last_name":  document.UpdatedBy.LastName,
			"email":      document.UpdatedBy.Email,
		}
	}

	// Add categories
	categories := make([]gin.H, len(document.Categories))
	for i, category := range document.Categories {
		categories[i] = gin.H{
			"id":   category.ID,
			"name": category.Name,
			"slug": category.Slug,
		}
	}
	response["categories"] = categories

	// Add tags
	tags := make([]gin.H, len(document.Tags))
	for i, tag := range document.Tags {
		tags[i] = gin.H{
			"id":   tag.ID,
			"name": tag.Name,
			"slug": tag.Slug,
		}
	}
	response["tags"] = tags

	// Add document categories
	docCategories := make([]gin.H, len(document.Categories))
	for i, category := range document.Categories {
		docCategories[i] = gin.H{
			"id":   category.ID,
			"name": category.Name,
			"slug": category.Slug,
		}
	}
	response["document_categories"] = docCategories

	// Add files
	files := make([]gin.H, len(document.Files))
	for i, file := range document.Files {
		files[i] = gin.H{
			"id":            file.ID,
			"filename":      file.FileName,
			"original_name": file.OriginalName,
			"file_size":     file.FileSize,
			"mime_type":     file.MimeType,
			"file_type":     file.FileType,
			"is_public":     file.IsPublic,
			"is_primary":    file.IsPrimary,
			"description":   file.Description,
			"created_at":    file.CreatedAt,
		}
	}
	response["files"] = files

	// Add recent comments
	comments := make([]gin.H, len(document.Comments))
	for i, comment := range document.Comments {
		commentData := gin.H{
			"id":              comment.ID,
			"content":         comment.Content,
			"commenter_name":  comment.CommenterName,
			"commenter_email": comment.CommenterEmail,
			"organization":    comment.Organization,
			"subject":         comment.Subject,
			"is_public":       comment.IsPublic,
			"is_verified":     comment.IsVerified,
			"is_moderated":    comment.IsModerated,
			"created_at":      comment.CreatedAt,
		}
		comments[i] = commentData
	}
	response["comments"] = comments

	// Add reviews
	reviews := make([]gin.H, len(document.Reviews))
	for i, review := range document.Reviews {
		reviewData := gin.H{
			"id":          review.ID,
			"status":      review.Status,
			"comments":    review.Comments,
			"rating":      review.Rating,
			"reviewed_at": review.ReviewedAt,
			"created_at":  review.CreatedAt,
		}
		if review.Reviewer.ID != 0 {
			reviewData["reviewer"] = gin.H{
				"id":         review.Reviewer.ID,
				"first_name": review.Reviewer.FirstName,
				"last_name":  review.Reviewer.LastName,
				"email":      review.Reviewer.Email,
			}
		}
		reviews[i] = reviewData
	}
	response["reviews"] = reviews

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document retrieved successfully",
		Data:    response,
	})
}

// UpdateDocument updates an existing document
func UpdateDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Title                string   `json:"title"`
		Abstract             string   `json:"abstract"`
		Content              string   `json:"content"`
		Type                 string   `json:"type"`
		CategoryIDs          []uint   `json:"category_ids"`
		TagIDs               []uint   `json:"tag_ids"`
		SubjectIDs           []uint   `json:"subject_ids"`
		PublicationDate      string   `json:"publication_date"`
		EffectiveDate        string   `json:"effective_date"`
		TerminationDate      string   `json:"termination_date"`
		CommentDueDate       string   `json:"comment_due_date"`
		FRDocumentNumber     string   `json:"fr_document_number"`
		FRCitation           string   `json:"fr_citation"`
		CFRCitations         []string `json:"cfr_citations"`
		DocketNumber         string   `json:"docket_number"`
		RegulatoryIdentifier string   `json:"regulatory_identifier"`
		AcceptsComments      *bool    `json:"accepts_comments"`
		CommentInstructions  string   `json:"comment_instructions"`
		PublicHearingDate    string   `json:"public_hearing_date"`
		PublicHearingInfo    string   `json:"public_hearing_info"`
		SignificantRule      *bool    `json:"significant_rule"`
		EconomicImpact       string   `json:"economic_impact"`
		SmallEntityImpact    *bool    `json:"small_entity_impact"`
		PageCount            *int     `json:"page_count"`
		WordCount            *int     `json:"word_count"`
		Language             string   `json:"language"`
		OriginalFormat       string   `json:"original_format"`
		FileSize             *int64   `json:"file_size"`
		Checksum             string   `json:"checksum"`
		VisibilityLevel      *int     `json:"visibility_level"`
		IsPublic             *bool    `json:"is_public"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find existing document
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Prepare updates
	updates := make(map[string]interface{})

	if req.Title != "" {
		updates["title"] = req.Title
		updates["slug"] = generateDocumentSlug(req.Title)
	}

	if req.Abstract != "" {
		updates["abstract"] = req.Abstract
	}

	if req.Content != "" {
		updates["content"] = req.Content
	}

	if req.Type != "" {
		updates["type"] = req.Type
	}

	if req.FRDocumentNumber != "" {
		updates["fr_document_number"] = req.FRDocumentNumber
	}

	if req.FRCitation != "" {
		updates["fr_citation"] = req.FRCitation
	}

	if len(req.CFRCitations) > 0 {
		updates["cfr_citations"] = strings.Join(req.CFRCitations, ",")
	}

	if req.DocketNumber != "" {
		updates["docket_number"] = req.DocketNumber
	}

	if req.RegulatoryIdentifier != "" {
		updates["regulatory_identifier"] = req.RegulatoryIdentifier
	}

	if req.CommentInstructions != "" {
		updates["comment_instructions"] = req.CommentInstructions
	}

	if req.PublicHearingInfo != "" {
		updates["public_hearing_info"] = req.PublicHearingInfo
	}

	if req.EconomicImpact != "" {
		updates["economic_impact"] = req.EconomicImpact
	}

	if req.Language != "" {
		updates["language"] = req.Language
	}

	if req.OriginalFormat != "" {
		updates["original_format"] = req.OriginalFormat
	}

	if req.Checksum != "" {
		updates["checksum"] = req.Checksum
	}

	// Handle boolean fields
	if req.AcceptsComments != nil {
		updates["accepts_comments"] = *req.AcceptsComments
	}

	if req.SignificantRule != nil {
		updates["significant_rule"] = *req.SignificantRule
	}

	if req.SmallEntityImpact != nil {
		updates["small_entity_impact"] = *req.SmallEntityImpact
	}

	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}

	// Handle numeric fields
	if req.PageCount != nil {
		updates["page_count"] = *req.PageCount
	}

	if req.WordCount != nil {
		updates["word_count"] = *req.WordCount
	}

	if req.FileSize != nil {
		updates["file_size"] = *req.FileSize
	}

	if req.VisibilityLevel != nil {
		updates["visibility_level"] = *req.VisibilityLevel
	}

	// Parse and update dates
	if req.PublicationDate != "" {
		if pubDate, err := time.Parse("2006-01-02", req.PublicationDate); err == nil {
			updates["publication_date"] = pubDate
		}
	}

	if req.EffectiveDate != "" {
		if effDate, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			updates["effective_date"] = effDate
		}
	}

	if req.TerminationDate != "" {
		if termDate, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			updates["termination_date"] = termDate
		}
	}

	if req.CommentDueDate != "" {
		if commentDate, err := time.Parse("2006-01-02", req.CommentDueDate); err == nil {
			updates["comment_due_date"] = commentDate
		}
	}

	if req.PublicHearingDate != "" {
		if hearingDate, err := time.Parse("2006-01-02T15:04:05Z", req.PublicHearingDate); err == nil {
			updates["public_hearing_date"] = hearingDate
		}
	}

	// Add updated by and timestamp
	updates["updated_by_id"] = userID.(uint)

	// Start transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update document
	if len(updates) > 0 {
		if err := tx.Model(&document).Updates(updates).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "Failed to update document",
				Message: err.Error(),
			})
			return
		}
	}

	// Update associations if provided
	if len(req.CategoryIDs) > 0 {
		var categories []models.Category
		if err := tx.Where("id IN ?", req.CategoryIDs).Find(&categories).Error; err == nil {
			if err := tx.Model(&document).Association("Categories").Replace(categories); err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "Failed to update categories",
					Message: err.Error(),
				})
				return
			}
		}
	}

	if len(req.TagIDs) > 0 {
		var tags []models.Tag
		if err := tx.Where("id IN ?", req.TagIDs).Find(&tags).Error; err == nil {
			if err := tx.Model(&document).Association("Tags").Replace(tags); err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "Failed to update tags",
					Message: err.Error(),
				})
				return
			}
		}
	}

	if len(req.SubjectIDs) > 0 {
		var subjects []models.Subject
		if err := tx.Where("id IN ?", req.SubjectIDs).Find(&subjects).Error; err == nil {
			if err := tx.Model(&document).Association("Subjects").Replace(subjects); err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "Failed to update subjects",
					Message: err.Error(),
				})
				return
			}
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to commit transaction",
			Message: err.Error(),
		})
		return
	}

	// Create summary for document update
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.CreateDocumentSummary(&document, models.ActionTypeUpdate, userID.(uint)); err != nil {
		// Log the error but don't fail the document update
		fmt.Printf("Warning: Failed to create summary for document update %d: %v\n", id, err)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document updated successfully",
		Data:    gin.H{"id": id},
	})
}

// DeleteDocument deletes a document
func DeleteDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get user ID from context
	_, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find existing document
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Start transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Remove associations
	if err := tx.Model(&document).Association("Categories").Clear(); err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to clear categories",
			Message: err.Error(),
		})
		return
	}

	if err := tx.Model(&document).Association("Tags").Clear(); err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to clear tags",
			Message: err.Error(),
		})
		return
	}

	if err := tx.Model(&document).Association("Subjects").Clear(); err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to clear subjects",
			Message: err.Error(),
		})
		return
	}

	// Delete related files
	if err := tx.Where("document_id = ?", id).Delete(&models.DocumentFile{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete document files",
			Message: err.Error(),
		})
		return
	}

	// Delete related comments
	if err := tx.Where("document_id = ?", id).Delete(&models.DocumentComment{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete document comments",
			Message: err.Error(),
		})
		return
	}

	// Delete related reviews
	if err := tx.Where("document_id = ?", id).Delete(&models.DocumentReview{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete document reviews",
			Message: err.Error(),
		})
		return
	}

	// Delete the document
	if err := tx.Delete(&document).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete document",
			Message: err.Error(),
		})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to commit transaction",
			Message: err.Error(),
		})
		return
	}

	// Delete related summaries
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.DeleteSummariesForEntity(models.EntityTypeDocument, uint(id)); err != nil {
		// Log the error but don't fail the document deletion
		fmt.Printf("Warning: Failed to delete summaries for document %d: %v\n", id, err)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document deleted successfully",
	})
}

// TrackDocumentView tracks document view for analytics
func TrackDocumentView(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find the document and increment view count
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Increment view count
	if err := db.Model(&document).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to update view count",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "View tracked successfully",
		Data:    gin.H{"views": document.ViewCount + 1},
	})
}

// UploadDocumentFile uploads a file for a document
func UploadDocumentFile(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Get uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		HandleBadRequest(c, "No file uploaded")
		return
	}
	defer file.Close()

	// Validate file type and size
	allowedTypes := map[string]bool{
		"application/pdf":    true,
		"application/msword": true,
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document": true,
		"text/plain": true,
	}

	contentType := header.Header.Get("Content-Type")
	if !allowedTypes[contentType] {
		HandleBadRequest(c, "File type not allowed. Only PDF, DOC, DOCX, and TXT files are permitted")
		return
	}

	// Check file size (max 10MB)
	if header.Size > 10*1024*1024 {
		HandleBadRequest(c, "File size too large. Maximum size is 10MB")
		return
	}

	// Get optional description
	description := c.PostForm("description")
	isPrimary := c.PostForm("is_primary") == "true"

	// Create document file record
	documentFile := &models.DocumentFile{
		DocumentID:   id,
		FileName:     header.Filename,
		OriginalName: header.Filename,
		FileSize:     header.Size,
		MimeType:     contentType,
		FileType:     strings.TrimPrefix(filepath.Ext(header.Filename), "."),
		UploadedByID: userID.(uint),
		FilePath:     fmt.Sprintf("documents/%d/%s", id, header.Filename), // This would be actual file path in production
		Description:  description,
		IsPublic:     true,
		IsPrimary:    isPrimary,
	}

	// Save file record to database
	if err := db.Create(documentFile).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to save file record",
			Message: err.Error(),
		})
		return
	}

	// Initialize file storage service if not already done
	if fileStorageService == nil {
		fileStorageService = services.NewFileStorageService(getConfig())
	}

	// Store the actual file
	storedFile, err := fileStorageService.StoreFile(header, fmt.Sprintf("documents/%d", id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to store file",
			Message: err.Error(),
		})
		return
	}

	// Update file record with actual storage information
	documentFile.FilePath = storedFile.Path
	documentFile.Checksum = storedFile.Checksum
	documentFile.FileSize = storedFile.Size
	if storedFile.MimeType != "" {
		documentFile.MimeType = storedFile.MimeType
	}

	// Update the database record with actual file information
	if err := db.Save(documentFile).Error; err != nil {
		// Clean up stored file if database update fails
		fileStorageService.DeleteFile(storedFile.Path)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to update file record",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "File uploaded successfully",
		Data: gin.H{
			"file_id":  documentFile.ID,
			"filename": documentFile.FileName,
			"size":     documentFile.FileSize,
		},
	})
}

// PublishDocument publishes a document
func PublishDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find document with agency
	var document models.Document
	if err := db.Preload("Agency").First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Check if document can be published
	if document.Status == models.StatusPublished {
		HandleBadRequest(c, "Document is already published")
		return
	}

	// Update document status and generate FR number if not exists
	now := time.Now()
	publishedByID := userID.(uint)
	document.Status = models.StatusPublished
	document.PublishedAt = &now
	document.PublishedByID = &publishedByID

	// Set publication date if not already set
	if document.PublicationDate == nil {
		document.PublicationDate = &now
	}

	// Generate FR document number if not exists
	if document.FRDocumentNumber == "" {
		year := now.Year()
		var count int64
		db.Model(&models.Document{}).Where("EXTRACT(YEAR FROM created_at) = ?", year).Count(&count)
		document.FRDocumentNumber = fmt.Sprintf("%d-%03d-%04d", year, 1, count+1)
	}

	// Generate docket number if not exists
	if document.DocketNumber == "" && document.Agency.ID != 0 {
		var agency models.Agency
		if err := db.First(&agency, document.AgencyID).Error; err == nil {
			var count int64
			db.Model(&models.Document{}).Where("agency_id = ?", document.AgencyID).Count(&count)
			document.DocketNumber = fmt.Sprintf("%s-%s-%s-%04d",
				agency.ShortName, "CAT", string(document.Type), count+1)
		}
	}

	if err := db.Save(&document).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to publish document",
			Message: err.Error(),
		})
		return
	}

	// Create summary for document publication
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.CreateDocumentSummary(&document, models.ActionTypePublish, userID.(uint)); err != nil {
		// Log the error but don't fail the document publication
		fmt.Printf("Warning: Failed to create summary for document publication %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document published successfully",
		Data: gin.H{
			"id":                 document.ID,
			"status":             document.Status,
			"fr_document_number": document.FRDocumentNumber,
			"docket_number":      document.DocketNumber,
			"published_at":       document.PublishedAt,
		},
	})
}

// SearchDocuments provides advanced search functionality
func SearchDocuments(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get pagination and search parameters
	pagination := GetPaginationParams(c)

	// Build search query
	query := db.Model(&models.Document{})

	// Apply search filters
	if searchQuery := c.Query("query"); searchQuery != "" {
		query = query.Where("title ILIKE ? OR abstract ILIKE ? OR content ILIKE ? OR fr_document_number ILIKE ? OR docket_number ILIKE ?",
			"%"+searchQuery+"%", "%"+searchQuery+"%", "%"+searchQuery+"%",
			"%"+searchQuery+"%", "%"+searchQuery+"%")
	}

	if docType := c.Query("type"); docType != "" {
		query = query.Where("type = ?", docType)
	}

	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if agencyID := c.Query("agency_id"); agencyID != "" {
		query = query.Where("agency_id = ?", agencyID)
	}

	if categoryID := c.Query("category_id"); categoryID != "" {
		query = query.Joins("JOIN document_category_assignments ON documents.id = document_category_assignments.document_id").
			Where("document_category_assignments.category_id = ?", categoryID)
	}

	// Apply date filters
	if fromDate := c.Query("from_date"); fromDate != "" {
		if parsedDate, err := time.Parse("2006-01-02", fromDate); err == nil {
			query = query.Where("publication_date >= ?", parsedDate)
		}
	}

	if toDate := c.Query("to_date"); toDate != "" {
		if parsedDate, err := time.Parse("2006-01-02", toDate); err == nil {
			query = query.Where("publication_date <= ?", parsedDate)
		}
	}

	// Apply sorting
	orderBy := "created_at DESC"
	if sortBy := c.Query("sort_by"); sortBy != "" {
		direction := "ASC"
		if c.Query("sort_order") == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", sortBy, direction)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to count search results",
			Message: err.Error(),
		})
		return
	}

	// Get documents with pagination
	var documents []models.Document
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := query.Preload("Agency").
		Preload("CreatedBy").
		Preload("Categories").
		Preload("Tags").
		Order(orderBy).
		Offset(offset).
		Limit(pagination.PerPage).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to search documents",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, document := range documents {
		response := gin.H{
			"id":                 document.ID,
			"title":              document.Title,
			"slug":               document.Slug,
			"abstract":           document.Abstract,
			"type":               document.Type,
			"status":             document.Status,
			"publication_date":   document.PublicationDate,
			"effective_date":     document.EffectiveDate,
			"fr_document_number": document.FRDocumentNumber,
			"docket_number":      document.DocketNumber,
			"view_count":         document.ViewCount,
			"created_at":         document.CreatedAt,
		}

		if document.Agency.ID != 0 {
			response["agency"] = gin.H{
				"id":         document.Agency.ID,
				"name":       document.Agency.Name,
				"short_name": document.Agency.ShortName,
			}
		}

		if document.CreatedBy.ID != 0 {
			response["created_by"] = gin.H{
				"id":         document.CreatedBy.ID,
				"first_name": document.CreatedBy.FirstName,
				"last_name":  document.CreatedBy.LastName,
				"email":      document.CreatedBy.Email,
			}
		}

		// Add categories
		categories := make([]gin.H, len(document.Categories))
		for j, category := range document.Categories {
			categories[j] = gin.H{
				"id":   category.ID,
				"name": category.Name,
				"slug": category.Slug,
			}
		}
		response["categories"] = categories

		documentResponses[i] = response
	}

	// Create paginated response
	response := CreatePaginationResponse(documentResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// GetDocumentFiles returns files for a specific document
func GetDocumentFiles(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Get document files
	var files []models.DocumentFile
	if err := db.Where("document_id = ?", id).
		Preload("UploadedBy").
		Order("is_primary DESC, created_at DESC").
		Find(&files).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document files",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	fileResponses := make([]gin.H, len(files))
	for i, file := range files {
		response := gin.H{
			"id":            file.ID,
			"filename":      file.FileName,
			"original_name": file.OriginalName,
			"file_size":     file.FileSize,
			"mime_type":     file.MimeType,
			"file_type":     file.FileType,
			"description":   file.Description,
			"is_public":     file.IsPublic,
			"is_primary":    file.IsPrimary,
			"created_at":    file.CreatedAt,
		}

		if file.UploadedBy.ID != 0 {
			response["uploaded_by"] = gin.H{
				"id":    file.UploadedBy.ID,
				"email": file.UploadedBy.Email,
			}
		}

		fileResponses[i] = response
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document files retrieved successfully",
		Data:    fileResponses,
	})
}

// DeleteDocumentFile deletes a document file
func DeleteDocumentFile(c *gin.Context) {
	fileID, valid := ValidateID(c, "fileId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find the file
	var file models.DocumentFile
	if err := db.First(&file, fileID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "File")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch file",
			Message: err.Error(),
		})
		return
	}

	// Delete the file record
	if err := db.Delete(&file).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete file",
			Message: err.Error(),
		})
		return
	}

	// In production, you would also delete the actual file from disk/cloud storage

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "File deleted successfully",
	})
}

// SubmitDocument submits a document for review
func SubmitDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find document
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Check if document can be submitted
	if document.Status != models.StatusDraft {
		HandleBadRequest(c, "Only draft documents can be submitted for review")
		return
	}

	// Update document status
	now := time.Now()
	updatedByID := userID.(uint)
	if err := db.Model(&document).Updates(map[string]interface{}{
		"status":        models.StatusUnderReview,
		"submitted_at":  &now,
		"updated_by_id": updatedByID,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to submit document",
			Message: err.Error(),
		})
		return
	}

	// Create summary for document submission
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.CreateDocumentSummary(&document, "submit", userID.(uint)); err != nil {
		// Log the error but don't fail the document submission
		fmt.Printf("Warning: Failed to create summary for document submission %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document submitted for review successfully",
		Data: gin.H{
			"id":     document.ID,
			"status": models.StatusUnderReview,
		},
	})
}

// WithdrawDocument withdraws a published document
func WithdrawDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find document
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Check if document can be withdrawn
	if document.Status != models.StatusPublished {
		HandleBadRequest(c, "Only published documents can be withdrawn")
		return
	}

	// Update document status
	now := time.Now()
	updatedByID := userID.(uint)
	if err := db.Model(&document).Updates(map[string]interface{}{
		"status":        models.StatusWithdrawn,
		"withdrawn_at":  &now,
		"updated_by_id": updatedByID,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to withdraw document",
			Message: err.Error(),
		})
		return
	}

	// Create summary for document withdrawal
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.CreateDocumentSummary(&document, "withdraw", userID.(uint)); err != nil {
		// Log the error but don't fail the document withdrawal
		fmt.Printf("Warning: Failed to create summary for document withdrawal %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document withdrawn successfully",
		Data: gin.H{
			"id":     document.ID,
			"status": models.StatusWithdrawn,
		},
	})
}

// ApproveDocument approves a document under review
func ApproveDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find document
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Check if document can be approved
	if document.Status != models.StatusUnderReview {
		HandleBadRequest(c, "Only documents under review can be approved")
		return
	}

	// Update document status
	now := time.Now()
	updatedByID := userID.(uint)
	if err := db.Model(&document).Updates(map[string]interface{}{
		"status":         models.StatusApproved,
		"approved_at":    &now,
		"approved_by_id": &updatedByID,
		"updated_by_id":  updatedByID,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to approve document",
			Message: err.Error(),
		})
		return
	}

	// Create summary for document approval
	if documentSummaryService == nil {
		documentSummaryService = services.NewSummaryService(database.GetDB())
	}
	if err := documentSummaryService.CreateDocumentSummary(&document, "approve", userID.(uint)); err != nil {
		// Log the error but don't fail the document approval
		fmt.Printf("Warning: Failed to create summary for document approval %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document approved successfully",
		Data: gin.H{
			"id":     document.ID,
			"status": models.StatusApproved,
		},
	})
}

// GetPublicDocument returns a single public document by ID
func GetPublicDocument(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get document with related data
	var document models.Document
	if err := db.Preload("Agency").
		Preload("CreatedBy").
		Preload("Categories").
		Where("id = ? AND is_public = ? AND visibility_level = ?", id, true, 1).
		First(&document).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	response := gin.H{
		"id":                    document.ID,
		"title":                 document.Title,
		"slug":                  document.Slug,
		"abstract":              document.Abstract,
		"content":               document.Content,
		"type":                  document.Type,
		"status":                document.Status,
		"publication_date":      document.PublicationDate,
		"effective_date":        document.EffectiveDate,
		"termination_date":      document.TerminationDate,
		"comment_due_date":      document.CommentDueDate,
		"accepts_comments":      document.AcceptsComments,
		"comment_count":         document.CommentCount,
		"comment_instructions":  document.CommentInstructions,
		"public_hearing_date":   document.PublicHearingDate,
		"public_hearing_info":   document.PublicHearingInfo,
		"regulatory_identifier": document.RegulatoryIdentifier,
		"docket_number":         document.DocketNumber,
		"significant_rule":      document.SignificantRule,
		"economic_impact":       document.EconomicImpact,
		"small_entity_impact":   document.SmallEntityImpact,
		"page_count":            document.PageCount,
		"word_count":            document.WordCount,
		"language":              document.Language,
		"agency": gin.H{
			"id":   document.Agency.ID,
			"name": document.Agency.Name,
			"slug": document.Agency.Slug,
		},
		"created_at": document.CreatedAt,
		"updated_at": document.UpdatedAt,
	}

	// Add categories
	categories := make([]gin.H, len(document.Categories))
	for i, category := range document.Categories {
		categories[i] = gin.H{
			"id":   category.ID,
			"name": category.Name,
			"slug": category.Slug,
		}
	}
	response["categories"] = categories

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document retrieved successfully",
		Data:    response,
	})
}

// SearchPublicDocuments searches public documents
func SearchPublicDocuments(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing search query",
			Message: "Query parameter 'q' is required",
		})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Build search query
	searchQuery := "%" + query + "%"
	baseQuery := db.Model(&models.Document{}).
		Preload("Agency").
		Where("is_public = ? AND visibility_level = ?", true, 1).
		Where("title ILIKE ? OR abstract ILIKE ? OR content ILIKE ?", searchQuery, searchQuery, searchQuery)

	// Count total results
	var total int64
	baseQuery.Count(&total)

	// Get documents with pagination
	var documents []models.Document
	offset := (page - 1) * perPage
	if err := baseQuery.
		Order("publication_date DESC, created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to search documents",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, doc := range documents {
		documentResponses[i] = gin.H{
			"id":               doc.ID,
			"title":            doc.Title,
			"slug":             doc.Slug,
			"abstract":         doc.Abstract,
			"type":             doc.Type,
			"status":           doc.Status,
			"publication_date": doc.PublicationDate,
			"effective_date":   doc.EffectiveDate,
			"agency": gin.H{
				"id":   doc.Agency.ID,
				"name": doc.Agency.Name,
				"slug": doc.Agency.Slug,
			},
			"created_at": doc.CreatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       documentResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetPublicStats returns public statistics
func GetPublicStats(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	var totalDocuments int64
	db.Model(&models.Document{}).Where("is_public = ? AND visibility_level = ?", true, 1).Count(&totalDocuments)

	var totalAgencies int64
	db.Model(&models.Agency{}).Where("is_active = ?", true).Count(&totalAgencies)

	var totalCategories int64
	db.Model(&models.Category{}).Where("is_active = ?", true).Count(&totalCategories)

	stats := gin.H{
		"total_documents":  totalDocuments,
		"total_agencies":   totalAgencies,
		"total_categories": totalCategories,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public statistics retrieved successfully",
		Data:    stats,
	})
}

// GetDocumentFile returns a specific document file
func GetDocumentFile(c *gin.Context) {
	fileID, valid := ValidateID(c, "file_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find the file record
	var documentFile models.DocumentFile
	if err := db.Preload("Document").First(&documentFile, fileID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "File")
			return
		}
		HandleInternalError(c, "Failed to fetch file: "+err.Error())
		return
	}

	// Check if file is public or user has access
	userID, exists := c.Get("user_id")
	if !documentFile.IsPublic && (!exists || userID == nil) {
		HandleUnauthorized(c, "Access denied to private file")
		return
	}

	// Initialize file storage service if needed
	if fileStorageService == nil {
		fileStorageService = services.NewFileStorageService(getConfig())
	}

	// Get the file from storage
	file, err := fileStorageService.GetFile(documentFile.FilePath)
	if err != nil {
		HandleNotFound(c, "File not found in storage")
		return
	}
	defer file.Close()

	// Verify file integrity
	if documentFile.Checksum != "" {
		valid, err := fileStorageService.VerifyFileIntegrity(documentFile.FilePath, documentFile.Checksum)
		if err != nil || !valid {
			HandleInternalError(c, "File integrity check failed")
			return
		}
	}

	// Set appropriate headers
	c.Header("Content-Type", documentFile.MimeType)
	c.Header("Content-Disposition", fmt.Sprintf("inline; filename=\"%s\"", documentFile.OriginalName))
	c.Header("Content-Length", fmt.Sprintf("%d", documentFile.FileSize))
	c.Header("Cache-Control", "public, max-age=3600")

	// Stream the file
	c.DataFromReader(http.StatusOK, documentFile.FileSize, documentFile.MimeType, file, nil)
}

// DownloadFile handles file downloads
func DownloadFile(c *gin.Context) {
	fileID, valid := ValidateID(c, "file_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find the file record
	var documentFile models.DocumentFile
	if err := db.Preload("Document").First(&documentFile, fileID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "File")
			return
		}
		HandleInternalError(c, "Failed to fetch file: "+err.Error())
		return
	}

	// Check if file is public or user has access
	userID, exists := c.Get("user_id")
	if !documentFile.IsPublic && (!exists || userID == nil) {
		HandleUnauthorized(c, "Access denied to private file")
		return
	}

	// Initialize file storage service if needed
	if fileStorageService == nil {
		fileStorageService = services.NewFileStorageService(getConfig())
	}

	// Get the file from storage
	file, err := fileStorageService.GetFile(documentFile.FilePath)
	if err != nil {
		HandleNotFound(c, "File not found in storage")
		return
	}
	defer file.Close()

	// Verify file integrity
	if documentFile.Checksum != "" {
		valid, err := fileStorageService.VerifyFileIntegrity(documentFile.FilePath, documentFile.Checksum)
		if err != nil || !valid {
			HandleInternalError(c, "File integrity check failed")
			return
		}
	}

	// Set download headers
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", documentFile.OriginalName))
	c.Header("Content-Length", fmt.Sprintf("%d", documentFile.FileSize))

	// Stream the file for download
	c.DataFromReader(http.StatusOK, documentFile.FileSize, "application/octet-stream", file, nil)

	// Update download count
	go func() {
		db.Model(&documentFile).Update("download_count", gorm.Expr("download_count + ?", 1))
	}()
}

// GetDocumentDefaults returns default values for document creation
func GetDocumentDefaults(c *gin.Context) {
	defaults := gin.H{
		"type":             "rule",
		"status":           "draft",
		"language":         "en",
		"visibility_level": 1,
		"is_public":        false,
		"accepts_comments": true,
		"significant_rule": false,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document defaults retrieved successfully",
		Data:    defaults,
	})
}

// UpdateDocumentFile updates a document file
func UpdateDocumentFile(c *gin.Context) {
	fileID, valid := ValidateID(c, "fileId")
	if !valid {
		return
	}

	var req struct {
		Description string `json:"description"`
		IsPublic    *bool  `json:"is_public"`
		IsPrimary   *bool  `json:"is_primary"`
		SortOrder   *int   `json:"sort_order"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing file
	var file models.DocumentFile
	if err := db.Preload("Document").Preload("UploadedBy").First(&file, fileID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document file")
			return
		}
		HandleInternalError(c, "Failed to fetch document file: "+err.Error())
		return
	}

	// Check permissions (user must be the uploader or have edit permissions on the document)
	if file.UploadedByID != *userID.(*uint) {
		// Add proper permission checking for document editing
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			HandleInternalError(c, "Failed to fetch user: "+err.Error())
			return
		}

		// Get the associated document for permission checking
		var document models.Document
		if err := db.First(&document, file.DocumentID).Error; err != nil {
			HandleInternalError(c, "Failed to fetch document: "+err.Error())
			return
		}

		// Check if user has permission to edit the document
		canEdit := false
		if user.Role == models.RoleAdmin {
			canEdit = true
		} else if user.Role == models.RoleEditor || user.Role == models.RolePublisher {
			// Editors and publishers can edit documents from their agency or documents they created
			if document.CreatedByID == user.ID {
				canEdit = true
			} else if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
				canEdit = true
			}
		}

		if !canEdit {
			HandleUnauthorized(c, "You don't have permission to update this file")
			return
		}
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.IsPrimary != nil {
		updates["is_primary"] = *req.IsPrimary
		// If setting as primary, unset other primary files for this document
		if *req.IsPrimary {
			db.Model(&models.DocumentFile{}).Where("document_id = ? AND id != ?", file.DocumentID, fileID).Update("is_primary", false)
		}
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}

	// Update file
	if err := db.Model(&file).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update document file: "+err.Error())
		return
	}

	// Load updated file with relationships
	if err := db.Preload("Document").Preload("UploadedBy").First(&file, file.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated file: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document file updated successfully",
		Data:    file,
	})
}

// CreateDocumentReview creates a new document review
func CreateDocumentReview(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Status   string `json:"status" binding:"required"`
		Comments string `json:"comments"`
		Rating   *int   `json:"rating"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Validate status
	validStatuses := []string{"pending", "approved", "rejected", "needs_changes"}
	isValidStatus := false
	for _, status := range validStatuses {
		if req.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		HandleBadRequest(c, "Invalid status. Must be one of: pending, approved, rejected, needs_changes")
		return
	}

	// Validate rating if provided
	if req.Rating != nil && (*req.Rating < 1 || *req.Rating > 5) {
		HandleBadRequest(c, "Rating must be between 1 and 5")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Check if user already has a review for this document
	var existingReview models.DocumentReview
	if err := db.Where("document_id = ? AND reviewer_id = ?", id, userID).First(&existingReview).Error; err == nil {
		HandleBadRequest(c, "You have already reviewed this document")
		return
	}

	// Set rating default if not provided
	rating := 0
	if req.Rating != nil {
		rating = *req.Rating
	}

	// Create review
	review := models.DocumentReview{
		DocumentID: id,
		ReviewerID: *userID.(*uint),
		Status:     req.Status,
		Comments:   req.Comments,
		Rating:     rating,
	}

	// Set reviewed_at if status is not pending
	if req.Status != "pending" {
		now := time.Now()
		review.ReviewedAt = &now
	}

	if err := db.Create(&review).Error; err != nil {
		HandleInternalError(c, "Failed to create document review: "+err.Error())
		return
	}

	// Load the created review with relationships
	if err := db.Preload("Document").Preload("Reviewer").First(&review, review.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created review: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Document review created successfully",
		Data:    review,
	})
}

// GetDocumentReviews returns reviews for a document
func GetDocumentReviews(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Build query
	query := db.Model(&models.DocumentReview{}).Where("document_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Where("comments ILIKE ?", "%"+search.Query+"%")
	}

	// Filter by status if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// Filter by rating if provided
	if rating := c.Query("rating"); rating != "" {
		query = query.Where("rating = ?", rating)
	}

	// Filter by reviewer if provided
	if reviewerID := c.Query("reviewer_id"); reviewerID != "" {
		query = query.Where("reviewer_id = ?", reviewerID)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get reviews with relationships
	var reviews []models.DocumentReview
	if err := query.Preload("Document").Preload("Reviewer").Find(&reviews).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document reviews: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       reviews,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetDocumentVersions returns versions for a document
func GetDocumentVersions(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Build query
	query := db.Model(&models.DocumentVersion{}).Where("document_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR change_log ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Filter by current status if provided
	if current := c.Query("current"); current != "" {
		if current == "true" {
			query = query.Where("is_current = ?", true)
		} else if current == "false" {
			query = query.Where("is_current = ?", false)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("version_number DESC")
	}

	// Get versions with relationships
	var versions []models.DocumentVersion
	if err := query.Preload("Document").Preload("CreatedBy").Find(&versions).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document versions: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       versions,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateDocumentVersion creates a new document version
func CreateDocumentVersion(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Title     string `json:"title" binding:"required"`
		Content   string `json:"content" binding:"required"`
		ChangeLog string `json:"change_log"`
		IsCurrent *bool  `json:"is_current"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Get the next version number
	var maxVersion int
	db.Model(&models.DocumentVersion{}).Where("document_id = ?", id).Select("COALESCE(MAX(version_number), 0)").Scan(&maxVersion)
	nextVersion := maxVersion + 1

	// Set default for is_current
	isCurrent := false
	if req.IsCurrent != nil {
		isCurrent = *req.IsCurrent
	}

	// If setting as current, unset other current versions
	if isCurrent {
		db.Model(&models.DocumentVersion{}).Where("document_id = ?", id).Update("is_current", false)
	}

	// Create version
	version := models.DocumentVersion{
		DocumentID:    id,
		VersionNumber: nextVersion,
		Title:         req.Title,
		Content:       req.Content,
		ChangeLog:     req.ChangeLog,
		IsCurrent:     isCurrent,
		CreatedByID:   *userID.(*uint),
	}

	if err := db.Create(&version).Error; err != nil {
		HandleInternalError(c, "Failed to create document version: "+err.Error())
		return
	}

	// Load the created version with relationships
	if err := db.Preload("Document").Preload("CreatedBy").First(&version, version.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created version: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Document version created successfully",
		Data:    version,
	})
}

// GetDocumentRegulations returns regulations related to a document
func GetDocumentRegulations(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Build query for regulation relationships
	query := db.Model(&models.RegulationDocumentRelationship{}).Where("document_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Joins("JOIN laws_and_rules ON regulation_document_relationships.law_rule_id = laws_and_rules.id").
			Where("laws_and_rules.title ILIKE ? OR laws_and_rules.content ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Filter by relationship type if provided
	if relType := c.Query("relationship_type"); relType != "" {
		query = query.Where("relationship_type = ?", relType)
	}

	// Filter by active status if provided
	if active := c.Query("active"); active != "" {
		if active == "true" {
			query = query.Where("is_active = ?", true)
		} else if active == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get relationships with regulations
	var relationships []models.RegulationDocumentRelationship
	if err := query.Preload("LawRule").Preload("Document").Preload("CreatedBy").Find(&relationships).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document regulations: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       relationships,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetDocumentReview returns a specific document review
func GetDocumentReview(c *gin.Context) {
	reviewID, valid := ValidateID(c, "reviewId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get review with relationships
	var review models.DocumentReview
	if err := db.Preload("Document").Preload("Reviewer").First(&review, reviewID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document review")
			return
		}
		HandleInternalError(c, "Failed to fetch document review: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document review retrieved successfully",
		Data:    review,
	})
}

// UpdateDocumentReview updates a document review
func UpdateDocumentReview(c *gin.Context) {
	reviewID, valid := ValidateID(c, "reviewId")
	if !valid {
		return
	}

	var req struct {
		Status   *string `json:"status"`
		Comments *string `json:"comments"`
		Rating   *int    `json:"rating"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing review
	var review models.DocumentReview
	if err := db.First(&review, reviewID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document review")
			return
		}
		HandleInternalError(c, "Failed to fetch document review: "+err.Error())
		return
	}

	// Check permissions (user must be the reviewer)
	if review.ReviewerID != *userID.(*uint) {
		HandleUnauthorized(c, "You can only update your own reviews")
		return
	}

	// Validate status if provided
	if req.Status != nil {
		validStatuses := []string{"pending", "approved", "rejected", "needs_changes"}
		isValidStatus := false
		for _, status := range validStatuses {
			if *req.Status == status {
				isValidStatus = true
				break
			}
		}
		if !isValidStatus {
			HandleBadRequest(c, "Invalid status. Must be one of: pending, approved, rejected, needs_changes")
			return
		}
	}

	// Validate rating if provided
	if req.Rating != nil && (*req.Rating < 1 || *req.Rating > 5) {
		HandleBadRequest(c, "Rating must be between 1 and 5")
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Status != nil {
		updates["status"] = *req.Status
		// Set reviewed_at if status is not pending
		if *req.Status != "pending" {
			updates["reviewed_at"] = time.Now()
		}
	}
	if req.Comments != nil {
		updates["comments"] = *req.Comments
	}
	if req.Rating != nil {
		updates["rating"] = *req.Rating
	}

	// Update review
	if err := db.Model(&review).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update document review: "+err.Error())
		return
	}

	// Load updated review with relationships
	if err := db.Preload("Document").Preload("Reviewer").First(&review, review.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated review: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document review updated successfully",
		Data:    review,
	})
}

// DeleteDocumentReview deletes a document review
func DeleteDocumentReview(c *gin.Context) {
	reviewID, valid := ValidateID(c, "reviewId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing review
	var review models.DocumentReview
	if err := db.First(&review, reviewID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document review")
			return
		}
		HandleInternalError(c, "Failed to fetch document review: "+err.Error())
		return
	}

	// Check permissions (user must be the reviewer)
	if review.ReviewerID != *userID.(*uint) {
		HandleUnauthorized(c, "You can only delete your own reviews")
		return
	}

	// Delete review
	if err := db.Delete(&review).Error; err != nil {
		HandleInternalError(c, "Failed to delete document review: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document review deleted successfully",
		Data:    gin.H{"id": reviewID},
	})
}

// GetDocumentVersion returns a specific document version
func GetDocumentVersion(c *gin.Context) {
	versionID, valid := ValidateID(c, "versionId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get version with relationships
	var version models.DocumentVersion
	if err := db.Preload("Document").Preload("CreatedBy").First(&version, versionID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document version")
			return
		}
		HandleInternalError(c, "Failed to fetch document version: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document version retrieved successfully",
		Data:    version,
	})
}

// UpdateDocumentVersion updates a document version
func UpdateDocumentVersion(c *gin.Context) {
	versionID, valid := ValidateID(c, "versionId")
	if !valid {
		return
	}

	var req struct {
		Title     *string `json:"title"`
		Content   *string `json:"content"`
		ChangeLog *string `json:"change_log"`
		IsCurrent *bool   `json:"is_current"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing version
	var version models.DocumentVersion
	if err := db.First(&version, versionID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document version")
			return
		}
		HandleInternalError(c, "Failed to fetch document version: "+err.Error())
		return
	}

	// Check permissions (user must be the creator or have edit permissions)
	if version.CreatedByID != *userID.(*uint) {
		// Add proper permission checking for document editing
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			HandleInternalError(c, "Failed to fetch user: "+err.Error())
			return
		}

		// Get the associated document for permission checking
		var document models.Document
		if err := db.First(&document, version.DocumentID).Error; err != nil {
			HandleInternalError(c, "Failed to fetch document: "+err.Error())
			return
		}

		// Check if user has permission to edit the document
		canEdit := false
		if user.Role == models.RoleAdmin {
			canEdit = true
		} else if user.Role == models.RoleEditor || user.Role == models.RolePublisher {
			// Editors and publishers can edit documents from their agency or documents they created
			if document.CreatedByID == user.ID {
				canEdit = true
			} else if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
				canEdit = true
			}
		}

		if !canEdit {
			HandleUnauthorized(c, "You don't have permission to update this version")
			return
		}
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.ChangeLog != nil {
		updates["change_log"] = *req.ChangeLog
	}
	if req.IsCurrent != nil {
		updates["is_current"] = *req.IsCurrent
		// If setting as current, unset other current versions for this document
		if *req.IsCurrent {
			db.Model(&models.DocumentVersion{}).Where("document_id = ? AND id != ?", version.DocumentID, versionID).Update("is_current", false)
		}
	}

	// Update version
	if err := db.Model(&version).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update document version: "+err.Error())
		return
	}

	// Load updated version with relationships
	if err := db.Preload("Document").Preload("CreatedBy").First(&version, version.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated version: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document version updated successfully",
		Data:    version,
	})
}

// DeleteDocumentVersion deletes a document version
func DeleteDocumentVersion(c *gin.Context) {
	versionID, valid := ValidateID(c, "versionId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing version
	var version models.DocumentVersion
	if err := db.First(&version, versionID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document version")
			return
		}
		HandleInternalError(c, "Failed to fetch document version: "+err.Error())
		return
	}

	// Check permissions (user must be the creator or have edit permissions)
	if version.CreatedByID != *userID.(*uint) {
		// Add proper permission checking for document editing
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			HandleInternalError(c, "Failed to fetch user: "+err.Error())
			return
		}

		// Get the associated document for permission checking
		var document models.Document
		if err := db.First(&document, version.DocumentID).Error; err != nil {
			HandleInternalError(c, "Failed to fetch document: "+err.Error())
			return
		}

		// Check if user has permission to edit the document
		canEdit := false
		if user.Role == models.RoleAdmin {
			canEdit = true
		} else if user.Role == models.RoleEditor || user.Role == models.RolePublisher {
			// Editors and publishers can edit documents from their agency or documents they created
			if document.CreatedByID == user.ID {
				canEdit = true
			} else if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
				canEdit = true
			}
		}

		if !canEdit {
			HandleUnauthorized(c, "You don't have permission to delete this version")
			return
		}
	}

	// Prevent deletion of current version if it's the only version
	if version.IsCurrent {
		var versionCount int64
		db.Model(&models.DocumentVersion{}).Where("document_id = ?", version.DocumentID).Count(&versionCount)
		if versionCount == 1 {
			HandleBadRequest(c, "Cannot delete the only version of a document")
			return
		}
	}

	// Delete version
	if err := db.Delete(&version).Error; err != nil {
		HandleInternalError(c, "Failed to delete document version: "+err.Error())
		return
	}

	// If we deleted the current version, set the latest remaining version as current
	if version.IsCurrent {
		var latestVersion models.DocumentVersion
		if err := db.Where("document_id = ?", version.DocumentID).Order("version_number DESC").First(&latestVersion).Error; err == nil {
			db.Model(&latestVersion).Update("is_current", true)
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document version deleted successfully",
		Data:    gin.H{"id": versionID},
	})
}
