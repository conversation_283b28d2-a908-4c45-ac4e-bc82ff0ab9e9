"use strict";
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ "(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js");
/* harmony import */ var C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js");
/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js");
/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,BuildingOfficeIcon,CalendarIcon,ClipboardDocumentListIcon,ClockIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,MagnifyingGlassIcon,TagIcon,UserIcon!=!@heroicons/react/24/outline */ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js");
/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/Layout/Layout */ "(app-pages-browser)/./app/components/Layout/Layout.tsx");
/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../services/api */ "(app-pages-browser)/./app/services/api.ts");
/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();



var _jsxFileName = "C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\search\\page.tsx", _this = undefined;
var __jsx = (react__WEBPACK_IMPORTED_MODULE_3___default().createElement);

function ownKeys(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {
            (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}






var SearchPage = function SearchPage() {
    _s();
    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();
    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get('q')) || ''), query = _useState[0], setQuery = _useState[1];
    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), results = _useState2[0], setResults = _useState2[1];
    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), agencies = _useState3[0], setAgencies = _useState3[1];
    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), loading = _useState4[0], setLoading = _useState4[1];
    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''), error = _useState5[0], setError = _useState5[1];
    var _useState6 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), showFilters = _useState6[0], setShowFilters = _useState6[1];
    var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
        type: '',
        agency_id: '',
        category_id: '',
        date_from: '',
        date_to: '',
        sort: 'relevance',
        order: 'desc'
    }), filters = _useState7[0], setFilters = _useState7[1];
    var _useState8 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
        page: 1,
        per_page: 20,
        total: 0,
        total_pages: 0
    }), pagination = _useState8[0], setPagination = _useState8[1];
    var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), searchTime = _useState9[0], setSearchTime = _useState9[1];
    var performSearch = /*#__PURE__*/ function() {
        var _ref = (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__["default"])(/*#__PURE__*/ C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
            var _response$data, _response$data2, _response$data3, startTime, params, response, endTime, _err$response;
            return C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {
                while(1)switch(_context.prev = _context.next){
                    case 0:
                        if (query.trim()) {
                            _context.next = 2;
                            break;
                        }
                        return _context.abrupt("return");
                    case 2:
                        _context.prev = 2;
                        setLoading(true);
                        setError('');
                        startTime = Date.now();
                        params = _objectSpread({
                            q: query,
                            page: pagination.page,
                            per_page: pagination.per_page
                        }, filters);
                        _context.next = 9;
                        return _services_api__WEBPACK_IMPORTED_MODULE_8__["default"].search(params);
                    case 9:
                        response = _context.sent;
                        setResults(Array.isArray((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.results) ? response.data.results : []);
                        setPagination({
                            page: response.page || pagination.page,
                            per_page: response.per_page || pagination.per_page,
                            total: ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.total) || 0,
                            total_pages: response.total_pages || Math.ceil((((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.total) || 0) / pagination.per_page)
                        });
                        endTime = Date.now();
                        setSearchTime((endTime - startTime) / 1000);
                        _context.next = 19;
                        break;
                    case 16:
                        _context.prev = 16;
                        _context.t0 = _context["catch"](2);
                        setError(((_err$response = _context.t0.response) === null || _err$response === void 0 || (_err$response = _err$response.data) === null || _err$response === void 0 ? void 0 : _err$response.message) || 'Search failed. Please try again.');
                    case 19:
                        _context.prev = 19;
                        setLoading(false);
                        return _context.finish(19);
                    case 22:
                    case "end":
                        return _context.stop();
                }
            }, _callee, null, [
                [
                    2,
                    16,
                    19,
                    22
                ]
            ]);
        }));
        return function performSearch() {
            return _ref.apply(this, arguments);
        };
    }();
    var fetchAgencies = /*#__PURE__*/ function() {
        var _ref2 = (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__["default"])(/*#__PURE__*/ C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {
            var response;
            return C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {
                while(1)switch(_context2.prev = _context2.next){
                    case 0:
                        _context2.prev = 0;
                        _context2.next = 3;
                        return _services_api__WEBPACK_IMPORTED_MODULE_8__["default"].getPublicAgencies({
                            per_page: 100
                        });
                    case 3:
                        response = _context2.sent;
                        setAgencies(response.data);
                        _context2.next = 10;
                        break;
                    case 7:
                        _context2.prev = 7;
                        _context2.t0 = _context2["catch"](0);
                        console.error('Failed to fetch agencies:', _context2.t0);
                    case 10:
                    case "end":
                        return _context2.stop();
                }
            }, _callee2, null, [
                [
                    0,
                    7
                ]
            ]);
        }));
        return function fetchAgencies() {
            return _ref2.apply(this, arguments);
        };
    }();
    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({
        "SearchPage.useEffect": function() {
            if (query) {
                performSearch();
            }
        }
    }["SearchPage.useEffect"], [
        pagination.page,
        filters
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({
        "SearchPage.useEffect": function() {
            var initialQuery = searchParams === null || searchParams === void 0 ? void 0 : searchParams.get('q');
            if (initialQuery) {
                setQuery(initialQuery);
                performSearch();
            }
            fetchAgencies();
        }
    }["SearchPage.useEffect"], []);
    var handleSearch = function handleSearch(e) {
        e.preventDefault();
        setPagination(function(prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
                page: 1
            });
        });
        performSearch();
    };
    var handleFilterChange = function handleFilterChange(key, value) {
        setFilters(function(prev) {
            return _objectSpread(_objectSpread({}, prev), {}, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])({}, key, value));
        });
        setPagination(function(prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
                page: 1
            });
        });
    };
    var getResultIcon = function getResultIcon(type) {
        switch(type){
            case 'document':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__["default"], {
                    className: "h-5 w-5 text-blue-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 151,
                        columnNumber: 16
                    }
                });
            case 'agency':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__["default"], {
                    className: "h-5 w-5 text-purple-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 153,
                        columnNumber: 16
                    }
                });
            case 'category':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__["default"], {
                    className: "h-5 w-5 text-green-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 155,
                        columnNumber: 16
                    }
                });
            case 'regulation':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__["default"], {
                    className: "h-5 w-5 text-orange-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 157,
                        columnNumber: 16
                    }
                });
            case 'task':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__["default"], {
                    className: "h-5 w-5 text-orange-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 159,
                        columnNumber: 16
                    }
                });
            case 'calendar_event':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__["default"], {
                    className: "h-5 w-5 text-indigo-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 161,
                        columnNumber: 16
                    }
                });
            case 'proceeding':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__["default"], {
                    className: "h-5 w-5 text-pink-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 163,
                        columnNumber: 16
                    }
                });
            case 'user':
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__["default"], {
                    className: "h-5 w-5 text-gray-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 165,
                        columnNumber: 16
                    }
                });
            default:
                return __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__["default"], {
                    className: "h-5 w-5 text-gray-600",
                    __self: _this,
                    __source: {
                        fileName: _jsxFileName,
                        lineNumber: 167,
                        columnNumber: 16
                    }
                });
        }
    };
    var getResultLink = function getResultLink(result) {
        switch(result.type){
            case 'document':
                return "/documents/".concat(result.id);
            case 'agency':
                return "/agencies/".concat(result.id);
            case 'category':
                return "/categories/".concat(result.id);
            case 'regulation':
                return "/regulations/".concat(result.id);
            case 'task':
                return "/tasks/".concat(result.id);
            case 'calendar_event':
                return "/calendar";
            case 'proceeding':
                return "/proceedings/".concat(result.id);
            case 'user':
                return "/admin/users/".concat(result.id);
            default:
                return '#';
        }
    };
    var highlightText = function highlightText(text, highlights) {
        if (!highlights || highlights.length === 0) return text;
        var highlightedText = text;
        highlights.forEach(function(highlight) {
            var regex = new RegExp("(".concat(highlight, ")"), 'gi');
            highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
        });
        return __jsx("span", {
            dangerouslySetInnerHTML: {
                __html: highlightedText
            },
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 203,
                columnNumber: 12
            }
        });
    };
    return __jsx(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_7__["default"], {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 207,
            columnNumber: 5
        }
    }, __jsx("div", {
        className: "container-custom py-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 208,
            columnNumber: 7
        }
    }, __jsx("div", {
        className: "mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 210,
            columnNumber: 9
        }
    }, __jsx("h1", {
        className: "text-3xl font-bold text-gray-900 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 211,
            columnNumber: 11
        }
    }, "Search"), __jsx("p", {
        className: "text-gray-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 212,
            columnNumber: 11
        }
    }, "Search across documents, agencies, categories, and regulations")), __jsx("div", {
        className: "bg-white rounded-lg shadow-md p-6 mb-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 218,
            columnNumber: 9
        }
    }, __jsx("form", {
        onSubmit: handleSearch,
        className: "mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 219,
            columnNumber: 11
        }
    }, __jsx("div", {
        className: "flex gap-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 220,
            columnNumber: 13
        }
    }, __jsx("div", {
        className: "flex-1",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 221,
            columnNumber: 15
        }
    }, __jsx("div", {
        className: "relative",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 222,
            columnNumber: 17
        }
    }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__["default"], {
        className: "h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 223,
            columnNumber: 19
        }
    }), __jsx("input", {
        type: "text",
        placeholder: "Search for documents, agencies, regulations, tasks, calendar events, proceedings...",
        value: query,
        onChange: function onChange(e) {
            return setQuery(e.target.value);
        },
        className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 224,
            columnNumber: 19
        }
    }))), __jsx("button", {
        type: "submit",
        disabled: loading,
        className: "px-6 py-3 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 233,
            columnNumber: 15
        }
    }, loading ? 'Searching...' : 'Search'), __jsx("button", {
        type: "button",
        onClick: function onClick() {
            return setShowFilters(!showFilters);
        },
        className: "px-4 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 240,
            columnNumber: 15
        }
    }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__["default"], {
        className: "h-5 w-5",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 245,
            columnNumber: 17
        }
    })))), showFilters && __jsx("div", {
        className: "border-t border-gray-200 pt-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 252,
            columnNumber: 13
        }
    }, __jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 253,
            columnNumber: 15
        }
    }, __jsx("select", {
        value: filters.type,
        onChange: function onChange(e) {
            return handleFilterChange('type', e.target.value);
        },
        className: "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 254,
            columnNumber: 17
        }
    }, __jsx("option", {
        value: "",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 259,
            columnNumber: 19
        }
    }, "All Types"), __jsx("option", {
        value: "documents",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 260,
            columnNumber: 19
        }
    }, "Documents"), __jsx("option", {
        value: "agencies",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 261,
            columnNumber: 19
        }
    }, "Agencies"), __jsx("option", {
        value: "categories",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 262,
            columnNumber: 19
        }
    }, "Categories"), __jsx("option", {
        value: "regulations",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 263,
            columnNumber: 19
        }
    }, "Regulations"), __jsx("option", {
        value: "tasks",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 264,
            columnNumber: 19
        }
    }, "Tasks"), __jsx("option", {
        value: "calendar",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 265,
            columnNumber: 19
        }
    }, "Calendar Events"), __jsx("option", {
        value: "proceedings",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 266,
            columnNumber: 19
        }
    }, "Proceedings"), __jsx("option", {
        value: "users",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 267,
            columnNumber: 19
        }
    }, "Users")), __jsx("select", {
        value: filters.agency_id,
        onChange: function onChange(e) {
            return handleFilterChange('agency_id', e.target.value);
        },
        className: "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 270,
            columnNumber: 17
        }
    }, __jsx("option", {
        value: "",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 275,
            columnNumber: 19
        }
    }, "All Agencies"), agencies.map(function(agency) {
        return __jsx("option", {
            key: agency.id,
            value: agency.id.toString(),
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 277,
                columnNumber: 21
            }
        }, agency.short_name || agency.name);
    })), __jsx("input", {
        type: "date",
        value: filters.date_from,
        onChange: function onChange(e) {
            return handleFilterChange('date_from', e.target.value);
        },
        className: "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500",
        placeholder: "From Date",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 283,
            columnNumber: 17
        }
    }), __jsx("input", {
        type: "date",
        value: filters.date_to,
        onChange: function onChange(e) {
            return handleFilterChange('date_to', e.target.value);
        },
        className: "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500",
        placeholder: "To Date",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 291,
            columnNumber: 17
        }
    }), __jsx("select", {
        value: filters.sort,
        onChange: function onChange(e) {
            return handleFilterChange('sort', e.target.value);
        },
        className: "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 299,
            columnNumber: 17
        }
    }, __jsx("option", {
        value: "relevance",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 304,
            columnNumber: 19
        }
    }, "Relevance"), __jsx("option", {
        value: "date",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 305,
            columnNumber: 19
        }
    }, "Date"), __jsx("option", {
        value: "title",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 306,
            columnNumber: 19
        }
    }, "Title"), __jsx("option", {
        value: "agency",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 307,
            columnNumber: 19
        }
    }, "Agency"))))), error && __jsx("div", {
        className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 316,
            columnNumber: 11
        }
    }, error), query && !loading && __jsx("div", {
        className: "mb-6",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 322,
            columnNumber: 11
        }
    }, __jsx("div", {
        className: "flex items-center justify-between",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 323,
            columnNumber: 13
        }
    }, __jsx("p", {
        className: "text-gray-600",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 324,
            columnNumber: 15
        }
    }, pagination.total > 0 ? __jsx((react__WEBPACK_IMPORTED_MODULE_3___default().Fragment), null, "About ", pagination.total.toLocaleString(), " results for \"", query, "\" (", searchTime.toFixed(2), " seconds)") : "No results found for \"".concat(query, "\"")))), loading ? __jsx("div", {
        className: "space-y-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 339,
            columnNumber: 11
        }
    }, (0,C_Users_ASUS_Documents_augment_projects_NoteControl_frontend_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__["default"])(Array(5)).map(function(_, i) {
        return __jsx("div", {
            key: i,
            className: "bg-white rounded-lg shadow-md p-6 animate-pulse",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 341,
                columnNumber: 15
            }
        }, __jsx("div", {
            className: "h-6 bg-gray-200 rounded mb-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 342,
                columnNumber: 17
            }
        }), __jsx("div", {
            className: "h-4 bg-gray-200 rounded mb-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 343,
                columnNumber: 17
            }
        }), __jsx("div", {
            className: "h-4 bg-gray-200 rounded mb-4 w-3/4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 344,
                columnNumber: 17
            }
        }), __jsx("div", {
            className: "flex space-x-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 345,
                columnNumber: 17
            }
        }, __jsx("div", {
            className: "h-4 bg-gray-200 rounded w-20",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 346,
                columnNumber: 19
            }
        }), __jsx("div", {
            className: "h-4 bg-gray-200 rounded w-24",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 347,
                columnNumber: 19
            }
        })));
    })) : results.length === 0 && query ? __jsx("div", {
        className: "text-center py-12",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 353,
            columnNumber: 11
        }
    }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__["default"], {
        className: "h-12 w-12 text-gray-400 mx-auto mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 354,
            columnNumber: 13
        }
    }), __jsx("h3", {
        className: "text-lg font-medium text-gray-900 mb-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 355,
            columnNumber: 13
        }
    }, "No results found"), __jsx("p", {
        className: "text-gray-600 mb-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 356,
            columnNumber: 13
        }
    }, "Try adjusting your search terms or filters."), __jsx("div", {
        className: "text-sm text-gray-500",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 359,
            columnNumber: 13
        }
    }, __jsx("p", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 360,
            columnNumber: 15
        }
    }, "Search tips:"), __jsx("ul", {
        className: "mt-2 space-y-1",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 361,
            columnNumber: 15
        }
    }, __jsx("li", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 362,
            columnNumber: 17
        }
    }, "\u2022 Try different keywords"), __jsx("li", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 363,
            columnNumber: 17
        }
    }, "\u2022 Check your spelling"), __jsx("li", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 364,
            columnNumber: 17
        }
    }, "\u2022 Use broader search terms"), __jsx("li", {
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 365,
            columnNumber: 17
        }
    }, "\u2022 Remove filters to see more results")))) : __jsx("div", {
        className: "space-y-4",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 370,
            columnNumber: 11
        }
    }, results.map(function(result) {
        return __jsx("div", {
            key: "".concat(result.type, "-").concat(result.id),
            className: "bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 372,
                columnNumber: 15
            }
        }, __jsx("div", {
            className: "p-6",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 373,
                columnNumber: 17
            }
        }, __jsx("div", {
            className: "flex items-start justify-between mb-3",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 374,
                columnNumber: 19
            }
        }, __jsx("div", {
            className: "flex items-center space-x-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 375,
                columnNumber: 21
            }
        }, getResultIcon(result.type), __jsx("span", {
            className: "text-sm font-medium text-gray-500 capitalize",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 377,
                columnNumber: 23
            }
        }, result.type), result.relevance_score && __jsx("span", {
            className: "text-xs text-gray-400",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 381,
                columnNumber: 25
            }
        }, "(", Math.round(result.relevance_score * 100), "% match)")), __jsx((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {
            href: getResultLink(result),
            className: "p-2 text-gray-400 hover:text-primary-600 transition-colors",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 386,
                columnNumber: 21
            }
        }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__["default"], {
            className: "h-4 w-4",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 390,
                columnNumber: 23
            }
        }))), __jsx("h3", {
            className: "text-xl font-semibold text-gray-900 mb-2",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 394,
                columnNumber: 19
            }
        }, __jsx((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {
            href: getResultLink(result),
            className: "hover:text-primary-600 transition-colors",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 395,
                columnNumber: 21
            }
        }, highlightText(result.title, result.highlights))), __jsx("p", {
            className: "text-gray-600 mb-4 line-clamp-3",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 403,
                columnNumber: 19
            }
        }, highlightText(result.description || result.summary || result.content || 'No description available', result.highlights)), __jsx("div", {
            className: "flex flex-wrap items-center gap-4 text-sm text-gray-500",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 410,
                columnNumber: 19
            }
        }, result.agency && __jsx("span", {
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 412,
                columnNumber: 23
            }
        }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__["default"], {
            className: "h-4 w-4 mr-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 413,
                columnNumber: 25
            }
        }), __jsx((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {
            href: "/agencies/".concat(result.agency.id),
            className: "hover:text-primary-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 414,
                columnNumber: 25
            }
        }, result.agency.name)), result.category && __jsx("span", {
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 423,
                columnNumber: 23
            }
        }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__["default"], {
            className: "h-4 w-4 mr-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 424,
                columnNumber: 25
            }
        }), __jsx((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {
            href: "/categories/".concat(result.category.id),
            className: "hover:text-primary-600",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 425,
                columnNumber: 25
            }
        }, result.category.name)), result.publication_date && __jsx("span", {
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 434,
                columnNumber: 23
            }
        }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__["default"], {
            className: "h-4 w-4 mr-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 435,
                columnNumber: 25
            }
        }), "Published: ", new Date(result.publication_date).toLocaleDateString()), __jsx("span", {
            className: "flex items-center",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 439,
                columnNumber: 21
            }
        }, __jsx(_barrel_optimize_names_AdjustmentsHorizontalIcon_BuildingOfficeIcon_CalendarIcon_ClipboardDocumentListIcon_ClockIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_MagnifyingGlassIcon_TagIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__["default"], {
            className: "h-4 w-4 mr-1",
            __self: _this,
            __source: {
                fileName: _jsxFileName,
                lineNumber: 440,
                columnNumber: 23
            }
        }), "Updated: ", new Date(result.updated_at).toLocaleDateString()))));
    })), pagination.total_pages > 1 && __jsx("div", {
        className: "flex items-center justify-between mt-8",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 452,
            columnNumber: 11
        }
    }, __jsx("div", {
        className: "text-sm text-gray-700",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 453,
            columnNumber: 13
        }
    }, "Showing ", (pagination.page - 1) * pagination.per_page + 1, " to", ' ', Math.min(pagination.page * pagination.per_page, pagination.total), " of", ' ', pagination.total, " results"), __jsx("div", {
        className: "flex space-x-2",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 458,
            columnNumber: 13
        }
    }, __jsx("button", {
        onClick: function onClick() {
            return setPagination(function(prev) {
                return _objectSpread(_objectSpread({}, prev), {}, {
                    page: prev.page - 1
                });
            });
        },
        disabled: pagination.page === 1,
        className: "px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 459,
            columnNumber: 15
        }
    }, "Previous"), __jsx("span", {
        className: "px-3 py-2 text-sm text-gray-700",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 466,
            columnNumber: 15
        }
    }, "Page ", pagination.page, " of ", pagination.total_pages), __jsx("button", {
        onClick: function onClick() {
            return setPagination(function(prev) {
                return _objectSpread(_objectSpread({}, prev), {}, {
                    page: prev.page + 1
                });
            });
        },
        disabled: pagination.page === pagination.total_pages,
        className: "px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
        __self: _this,
        __source: {
            fileName: _jsxFileName,
            lineNumber: 469,
            columnNumber: 15
        }
    }, "Next")))));
};
_s(SearchPage, "+e7iclH3aqJrvtpAQq0T8u/C34Y=", false, function() {
    return [
        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams
    ];
});
_c = SearchPage;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);
var _c;
$RefreshReg$(_c, "SearchPage");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});