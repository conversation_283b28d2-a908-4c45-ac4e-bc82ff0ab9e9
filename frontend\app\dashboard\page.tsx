'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ChartBarIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  UserGroupIcon,
  ClockIcon,
  EyeIcon,
  PlusIcon,
  CalendarIcon,
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';

interface DashboardStats {
  // Backend returns flat structure
  total_documents: number;
  published_documents: number;
  draft_documents: number;
  under_review: number;
  total_agencies: number;
  active_users: number;
  total_users: number;
  total_categories: number;
  my_documents: number;
  documents_this_month: number;
  recent_views: number;
  recent_activity: any[];
}

const DashboardPage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentDocuments, setRecentDocuments] = useState<any[]>([]);
  const [recentTasks, setRecentTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Only fetch data if authenticated - Layout component handles auth redirects
    if (!isAuthenticated) {
      return;
    }

    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch dashboard stats
        const statsResponse = await apiService.getDashboardStats();
        setStats(statsResponse.data);

        // Fetch recent documents
        const documentsResponse = await apiService.getDocuments({
          page: 1,
          per_page: 5,
          sort: 'created_at',
          order: 'desc'
        });
        setRecentDocuments(Array.isArray(documentsResponse.data) ? documentsResponse.data : []);

        // Fetch recent tasks
        const tasksResponse = await apiService.getTasks({
          page: 1,
          per_page: 5,
          order_by: 'created_at'
        });
        setRecentTasks(Array.isArray(tasksResponse.data) ? tasksResponse.data : []);

      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthenticated]);

  return (
    <Layout
      title="Dashboard - Federal Register Clone"
      requireAuth={true}
      allowedRoles={['viewer', 'editor', 'reviewer', 'publisher', 'admin']}
    >
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.first_name || user?.username}!
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your documents and tasks today.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-8 bg-gray-200 rounded mb-4"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Documents Stats */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Documents</h3>
                    <p className="text-2xl font-bold text-blue-600">{stats?.total_documents || 0}</p>
                    <p className="text-sm text-gray-500">
                      {stats?.published_documents || 0} published, {stats?.draft_documents || 0} draft
                    </p>
                  </div>
                </div>
              </div>

              {/* Tasks Stats */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Tasks</h3>
                    <p className="text-2xl font-bold text-green-600">{stats?.my_documents || 0}</p>
                    <p className="text-sm text-gray-500">
                      My documents and tasks
                    </p>
                  </div>
                </div>
              </div>

              {/* Agencies Stats */}
              {(user?.role === 'admin' || user?.role === 'editor') && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <BuildingOfficeIcon className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Agencies</h3>
                      <p className="text-2xl font-bold text-purple-600">{stats?.total_agencies || 0}</p>
                      <p className="text-sm text-gray-500">
                        {stats?.total_agencies || 0} active
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Categories Stats */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TagIcon className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Categories</h3>
                    <p className="text-2xl font-bold text-orange-600">{stats?.total_categories || 0}</p>
                    <p className="text-sm text-gray-500">Available categories</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {user?.role !== 'viewer' && (
                  <Link
                    href="/documents/new"
                    className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <DocumentTextIcon className="h-8 w-8 text-blue-600 mb-2" />
                    <span className="text-sm font-medium text-gray-900">New Document</span>
                  </Link>
                )}
                
                <Link
                  href="/tasks/new"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <PlusIcon className="h-8 w-8 text-green-600 mb-2" />
                  <span className="text-sm font-medium text-gray-900">New Task</span>
                </Link>

                <Link
                  href="/calendar"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <CalendarIcon className="h-8 w-8 text-purple-600 mb-2" />
                  <span className="text-sm font-medium text-gray-900">Calendar</span>
                </Link>

                <Link
                  href="/search"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <ChartBarIcon className="h-8 w-8 text-orange-600 mb-2" />
                  <span className="text-sm font-medium text-gray-900">Search</span>
                </Link>

                <Link
                  href="/profile"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <UserGroupIcon className="h-8 w-8 text-gray-600 mb-2" />
                  <span className="text-sm font-medium text-gray-900">Profile</span>
                </Link>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Recent Documents */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Recent Documents</h2>
                  <Link
                    href="/documents"
                    className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                  >
                    View all
                  </Link>
                </div>
                
                {(recentDocuments || []).length === 0 ? (
                  <div className="text-center py-8">
                    <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No recent documents</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {(recentDocuments || []).map((document) => (
                      <div key={document.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 truncate">
                            <Link 
                              href={`/documents/${document.id}`}
                              className="hover:text-primary-600"
                            >
                              {document.title}
                            </Link>
                          </h4>
                          <p className="text-sm text-gray-500">
                            {document.agency?.name} • {new Date(document.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Link
                          href={`/documents/${document.id}`}
                          className="p-2 text-gray-400 hover:text-primary-600"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Recent Tasks */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Recent Tasks</h2>
                  <Link
                    href="/tasks"
                    className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                  >
                    View all
                  </Link>
                </div>
                
                {(recentTasks || []).length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No recent tasks</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {(recentTasks || []).map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 truncate">
                            <Link 
                              href={`/tasks/${task.id}`}
                              className="hover:text-primary-600"
                            >
                              {task.title}
                            </Link>
                          </h4>
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              task.status === 'completed' ? 'bg-green-100 text-green-800' :
                              task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {task.status?.replace('_', ' ').toUpperCase()}
                            </span>
                            {task.due_date && (
                              <span className="flex items-center">
                                <ClockIcon className="h-3 w-3 mr-1" />
                                {new Date(task.due_date).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                        <Link
                          href={`/tasks/${task.id}`}
                          className="p-2 text-gray-400 hover:text-primary-600"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </Layout>
  );
};

export default DashboardPage;
