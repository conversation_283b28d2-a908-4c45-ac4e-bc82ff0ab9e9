"use strict";
exports.id = "vendor-chunks/math-intrinsics";
exports.ids = ["vendor-chunks/math-intrinsics"];
exports.modules = {

/***/ "(ssr)/./node_modules/math-intrinsics/abs.js":
/*!*********************************************!*\
  !*** ./node_modules/math-intrinsics/abs.js ***!
  \*********************************************/
/***/ ((module) => {



/** @type {import('./abs')} */
module.exports = Math.abs;


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/floor.js":
/*!***********************************************!*\
  !*** ./node_modules/math-intrinsics/floor.js ***!
  \***********************************************/
/***/ ((module) => {



/** @type {import('./floor')} */
module.exports = Math.floor;


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/isNaN.js":
/*!***********************************************!*\
  !*** ./node_modules/math-intrinsics/isNaN.js ***!
  \***********************************************/
/***/ ((module) => {



/** @type {import('./isNaN')} */
module.exports = Number.isNaN || function isNaN(a) {
	return a !== a;
};


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/max.js":
/*!*********************************************!*\
  !*** ./node_modules/math-intrinsics/max.js ***!
  \*********************************************/
/***/ ((module) => {



/** @type {import('./max')} */
module.exports = Math.max;


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/min.js":
/*!*********************************************!*\
  !*** ./node_modules/math-intrinsics/min.js ***!
  \*********************************************/
/***/ ((module) => {



/** @type {import('./min')} */
module.exports = Math.min;


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/pow.js":
/*!*********************************************!*\
  !*** ./node_modules/math-intrinsics/pow.js ***!
  \*********************************************/
/***/ ((module) => {



/** @type {import('./pow')} */
module.exports = Math.pow;


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/round.js":
/*!***********************************************!*\
  !*** ./node_modules/math-intrinsics/round.js ***!
  \***********************************************/
/***/ ((module) => {



/** @type {import('./round')} */
module.exports = Math.round;


/***/ }),

/***/ "(ssr)/./node_modules/math-intrinsics/sign.js":
/*!**********************************************!*\
  !*** ./node_modules/math-intrinsics/sign.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var $isNaN = __webpack_require__(/*! ./isNaN */ "(ssr)/./node_modules/math-intrinsics/isNaN.js");

/** @type {import('./sign')} */
module.exports = function sign(number) {
	if ($isNaN(number) || number === 0) {
		return number;
	}
	return number < 0 ? -1 : +1;
};


/***/ })

};
;