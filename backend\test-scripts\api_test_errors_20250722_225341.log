=== API COMPREHENSIVE TEST ERROR LOG ===
Test started at: 2025-07-22 22:53:41
===================================================
[22:53:43] auth | POST /auth/reset-password | Status: 400 | Type: VALIDATION_ERROR | Critical: false
  Error: HTTP 400 error: {"error":"Invalid or expired reset token","message":"token is malformed: token contains an invalid number of segments"}
  Response: {"error":"Invalid or expired reset token","message":"token is malformed: token contains an invalid number of segments"}

[22:53:43] auth | POST /auth/change-password | Status: 400 | Type: VALIDATION_ERROR | Critical: false
  Error: HTTP 400 error: {"error":"Invalid current password","message":"The current password you entered is incorrect"}
  Response: {"error":"Invalid current password","message":"The current password you entered is incorrect"}

[22:53:45] agencies | CREATE /agencies | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:46] categories | CREATE /categories | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:46] documents | CREATE /documents | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:47] regulations | CREATE /regulations | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:49] proceedings | CREATE /proceedings | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:50] finance | CREATE /finance | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:50] finance_categories | CREATE /finance/categories | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:51] users | GET /users | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:51] users | CREATE /users | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:52] summaries | CREATE /summaries | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:52] calendar_events | CREATE /calendar/events | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:54] tags | CREATE /tags | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:55] subjects | CREATE /subjects | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:56] comments | CREATE /documents/150/comments | Status: 400 | Type: VALIDATION_ERROR | Critical: false
  Error: HTTP 400 error: {"error":"Comments not accepted","message":"This document does not accept comments"}
  Response: {"error":"Comments not accepted","message":"This document does not accept comments"}

[22:53:56] analytics_dashboard | GET /analytics/dashboard | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:56] analytics_documents | GET /analytics/documents/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:56] analytics_agencies | GET /analytics/agencies/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:56] analytics_users | GET /analytics/users/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:56] analytics_relationships | GET /analytics/relationships/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:57] roles | GET /admin/roles | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:57] roles | CREATE /admin/roles | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:57] permissions | GET /admin/permissions | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:58] signatures | CREATE /documents | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:53:58] certificates | CREATE /certificates | Status: 500 | Type: SERVER_ERROR | Critical: true
  Error: HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create certificate: ERROR: column \"certificate_policies\" of relation \"digital_certificates\" does not exist (SQLSTATE 42703)"}
  Response: {"error":"Internal Server Error","message":"Failed to create certificate: ERROR: column \"certificate_policies\" of relation \"digital_certificates\" does not exist (SQLSTATE 42703)"}

[22:54:01] processing_jobs | CREATE /documents | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:54:04] enterprise_kpi | CREATE /enterprise/bi/kpis | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[22:54:05] public | GET_NO_AUTH /public/tags/1/documents | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Tag not found"}
  Response: {"error":"Not Found","message":"Tag not found"}

[22:54:06] document_files | GET /documents/1/files | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[22:54:06] document_files | GET /documents/1/metadata | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[22:54:06] document_analysis | GET /documents/1/metadata | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[22:54:06] document_analysis | GET /documents/1/classification | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[22:54:06] document_analysis | GET /documents/1/entities | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[22:54:07] auto_generation | POST /auto-generation/generate | Status: 500 | Type: SERVER_ERROR | Critical: true
  Error: HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create default auto-generation config: ERROR: column \"user_id\" of relation \"auto_generation_configs\" does not exist (SQLSTATE 42703)"}
  Response: {"error":"Internal Server Error","message":"Failed to create default auto-generation config: ERROR: column \"user_id\" of relation \"auto_generation_configs\" does not exist (SQLSTATE 42703)"}

[22:54:07] regulation_chunks | CREATE /regulations/1/chunks | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}


=== DETAILED ERROR ANALYSIS - 36 ERRORS FOUND ===

CRITICAL ERRORS (2):
  - certificates CREATE /certificates (Status: 500): HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create certificate: ERROR: column \"certificate_policies\" of relation \"digital_certificates\" does not exist (SQLSTATE 42703)"}
  - auto_generation POST /auto-generation/generate (Status: 500): HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create default auto-generation config: ERROR: column \"user_id\" of relation \"auto_generation_configs\" does not exist (SQLSTATE 42703)"}

ERRORS BY TYPE:

SERVER_ERROR (2 errors):
  - certificates CREATE /certificates (Status: 500): HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create certificate: ERROR: column \"certificate_policies\" of relation \"digital_certificates\" does not exist (SQLSTATE 42703)"}
  - auto_generation POST /auto-generation/generate (Status: 500): HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create default auto-generation config: ERROR: column \"user_id\" of relation \"auto_generation_configs\" does not exist (SQLSTATE 42703)"}

NOT_FOUND_ERROR (6 errors):
  - public GET_NO_AUTH /public/tags/1/documents (Status: 404): HTTP 404 error: {"error":"Not Found","message":"Tag not found"}
  - document_files GET /documents/1/files (Status: 404): HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  - document_files GET /documents/1/metadata (Status: 404): HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  - document_analysis GET /documents/1/metadata (Status: 404): HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  - document_analysis GET /documents/1/classification (Status: 404): HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  - document_analysis GET /documents/1/entities (Status: 404): HTTP 404 error: {"error":"Not Found","message":"Document not found"}

VALIDATION_ERROR (3 errors):
  - auth POST /auth/reset-password (Status: 400): HTTP 400 error: {"error":"Invalid or expired reset token","message":"token is malformed: token contains an invalid number of segments"}
  - auth POST /auth/change-password (Status: 400): HTTP 400 error: {"error":"Invalid current password","message":"The current password you entered is incorrect"}
  - comments CREATE /documents/150/comments (Status: 400): HTTP 400 error: {"error":"Comments not accepted","message":"This document does not accept comments"}

PERMISSION_ERROR (25 errors):
  - agencies CREATE /agencies (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - categories CREATE /categories (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - documents CREATE /documents (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - regulations CREATE /regulations (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - proceedings CREATE /proceedings (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - finance CREATE /finance (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - finance_categories CREATE /finance/categories (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - users GET /users (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - users CREATE /users (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - summaries CREATE /summaries (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - calendar_events CREATE /calendar/events (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - tags CREATE /tags (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - subjects CREATE /subjects (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - analytics_dashboard GET /analytics/dashboard (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - analytics_documents GET /analytics/documents/stats (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - analytics_agencies GET /analytics/agencies/stats (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - analytics_users GET /analytics/users/stats (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - analytics_relationships GET /analytics/relationships/stats (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - roles GET /admin/roles (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - roles CREATE /admin/roles (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - permissions GET /admin/permissions (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - signatures CREATE /documents (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - processing_jobs CREATE /documents (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - enterprise_kpi CREATE /enterprise/bi/kpis (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}
  - regulation_chunks CREATE /regulations/1/chunks (Status: 403): HTTP 403 error: {"error":"Insufficient permissions"}

FIX RECOMMENDATIONS:

VALIDATION ISSUES:
  - Check request validation rules
  - Verify required fields
  - Update model validation tags
   Affected endpoints:
   - POST /auth/reset-password
   - POST /auth/change-password
   - CREATE /documents/150/comments
   Affected endpoints:
   - CREATE /agencies
   - CREATE /categories
   - CREATE /documents
   - CREATE /regulations
   - CREATE /proceedings
   - CREATE /finance
   - CREATE /finance/categories
   - GET /users
   - CREATE /users
   - CREATE /summaries
   - CREATE /calendar/events
   - CREATE /tags
   - CREATE /subjects
   - GET /analytics/dashboard
   - GET /analytics/documents/stats
   - GET /analytics/agencies/stats
   - GET /analytics/users/stats
   - GET /analytics/relationships/stats
   - GET /admin/roles
   - CREATE /admin/roles
   - GET /admin/permissions
   - CREATE /documents
   - CREATE /documents
   - CREATE /enterprise/bi/kpis
   - CREATE /regulations/1/chunks

SERVER ERRORS:
  - Check handler implementations
  - Verify database connections
  - Add proper error handling
   Affected endpoints:
   - CREATE /certificates
   - POST /auto-generation/generate

MISSING ENDPOINTS:
  - Implement missing API handlers
  - Add missing routes to router.go
  - Check URL patterns
   Affected endpoints:
   - GET_NO_AUTH /public/tags/1/documents
   - GET /documents/1/files
   - GET /documents/1/metadata
   - GET /documents/1/metadata
   - GET /documents/1/classification
   - GET /documents/1/entities
===================================================
Test completed at: 2025-07-22 22:54:09
=== END OF LOG ===
