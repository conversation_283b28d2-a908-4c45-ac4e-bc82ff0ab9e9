"use strict";
exports.id = "vendor-chunks/has-tostringtag";
exports.ids = ["vendor-chunks/has-tostringtag"];
exports.modules = {

/***/ "(ssr)/./node_modules/has-tostringtag/shams.js":
/*!***********************************************!*\
  !*** ./node_modules/has-tostringtag/shams.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var hasSymbols = __webpack_require__(/*! has-symbols/shams */ "(ssr)/./node_modules/has-symbols/shams.js");

/** @type {import('.')} */
module.exports = function hasToStringTagShams() {
	return hasSymbols() && !!Symbol.toStringTag;
};


/***/ })

};
;