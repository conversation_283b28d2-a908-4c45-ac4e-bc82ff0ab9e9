'use client'

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XMarkIcon,
  ArrowLeftIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';
import { Document } from '../../../types';

interface DocumentFormData {
  title: string;
  abstract: string;
  content: string;
  type: string;
  agency_id: string;
  category_ids: string[];
  tag_ids: string[];
  subject_ids: string[];
  publication_date: string;
  effective_date: string;
  termination_date: string;
  comment_due_date: string;
  fr_document_number: string;
  fr_citation: string;
  cfr_citations: string;
  docket_number: string;
  regulatory_identifier: string;
  accepts_comments: boolean;
  comment_instructions: string;
  public_hearing_date: string;
  public_hearing_info: string;
  page_count: number;
  word_count: number;
  language: string;
  original_format: string;
  file_size: number;
  checksum: string;
  visibility_level: number;
  is_public: boolean;
  significant_rule: boolean;
  economic_impact: string;
  small_entity_impact: boolean;
}

const EditDocumentPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [agencies, setAgencies] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [tags, setTags] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [document, setDocument] = useState<Document | null>(null);
  const [formData, setFormData] = useState<DocumentFormData>({
    title: '',
    abstract: '',
    content: '',
    type: 'rule',
    agency_id: '',
    category_ids: [],
    tag_ids: [],
    subject_ids: [],
    publication_date: '',
    effective_date: '',
    termination_date: '',
    comment_due_date: '',
    fr_document_number: '',
    fr_citation: '',
    cfr_citations: '',
    docket_number: '',
    regulatory_identifier: '',
    accepts_comments: true,
    comment_instructions: '',
    public_hearing_date: '',
    public_hearing_info: '',
    page_count: 0,
    word_count: 0,
    language: 'en',
    original_format: 'pdf',
    file_size: 0,
    checksum: '',
    visibility_level: 1,
    is_public: true,
    significant_rule: false,
    economic_impact: '',
    small_entity_impact: false,
  });

  const documentId = params?.id as string;

  useEffect(() => {
    if (!isAuthenticated || user?.role === 'viewer') {
      router.push('/dashboard');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch document
        const documentResponse = await apiService.getDocument(parseInt(documentId));
        setDocument(documentResponse);
        
        // Check if user can edit this document
        if (user?.role !== 'admin' && user?.role !== 'editor' && documentResponse.created_by_id !== user?.id) {
          router.push(`/documents/${documentId}`);
          return;
        }

        // Populate form with document data
        setFormData({
          title: documentResponse.title || '',
          abstract: documentResponse.abstract || '',
          content: documentResponse.content || '',
          type: documentResponse.type || 'rule',
          agency_id: documentResponse.agency_id?.toString() || '',
          category_ids: documentResponse.categories?.map(cat => cat.id.toString()) || [],
          tag_ids: documentResponse.tags?.map(tag => tag.id.toString()) || [],
          subject_ids: documentResponse.subjects?.map(subj => subj.id.toString()) || [],
          publication_date: documentResponse.publication_date ? documentResponse.publication_date.split('T')[0] : '',
          effective_date: documentResponse.effective_date ? documentResponse.effective_date.split('T')[0] : '',
          termination_date: documentResponse.termination_date ? documentResponse.termination_date.split('T')[0] : '',
          comment_due_date: documentResponse.comment_due_date ? documentResponse.comment_due_date.split('T')[0] : '',
          fr_document_number: documentResponse.fr_document_number || '',
          fr_citation: documentResponse.fr_citation || '',
          cfr_citations: documentResponse.cfr_citations || '',
          docket_number: documentResponse.docket_number || '',
          regulatory_identifier: documentResponse.regulatory_identifier || '',
          accepts_comments: documentResponse.accepts_comments || false,
          comment_instructions: documentResponse.comment_instructions || '',
          public_hearing_date: documentResponse.public_hearing_date ? documentResponse.public_hearing_date.split('T')[0] : '',
          public_hearing_info: documentResponse.public_hearing_info || '',
          page_count: documentResponse.page_count || 0,
          word_count: documentResponse.word_count || 0,
          language: documentResponse.language || 'en',
          original_format: documentResponse.original_format || 'pdf',
          file_size: documentResponse.file_size || 0,
          checksum: documentResponse.checksum || '',
          visibility_level: documentResponse.visibility_level || 1,
          is_public: documentResponse.is_public !== undefined ? documentResponse.is_public : true,
          significant_rule: documentResponse.significant_rule || false,
          economic_impact: documentResponse.economic_impact || '',
          small_entity_impact: documentResponse.small_entity_impact || false,
        });

        // Fetch agencies
        const agenciesResponse = await apiService.getAgencies({ per_page: 100 });
        setAgencies(agenciesResponse.data);

        // Fetch categories
        const categoriesResponse = await apiService.getCategories();
        setCategories(categoriesResponse.data);

        // Fetch tags
        const tagsResponse = await apiService.getTags();
        setTags(tagsResponse.data);

        // Fetch subjects
        const subjectsResponse = await apiService.getSubjects();
        setSubjects(subjectsResponse.data);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch document data');
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      fetchData();
    }
  }, [documentId, isAuthenticated, user, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    setFormData(prev => ({
      ...prev,
      category_ids: prev.category_ids.includes(categoryId)
        ? prev.category_ids.filter(id => id !== categoryId)
        : [...prev.category_ids, categoryId]
    }));
  };

  const handleTagChange = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...prev.tag_ids, tagId]
    }));
  };

  const handleSubjectChange = (subjectId: string) => {
    setFormData(prev => ({
      ...prev,
      subject_ids: prev.subject_ids.includes(subjectId)
        ? prev.subject_ids.filter(id => id !== subjectId)
        : [...prev.subject_ids, subjectId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!documentId) {
      setError('Document ID is missing');
      return;
    }

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      const updateData = {
        ...formData,
        agency_id: formData.agency_id ? parseInt(formData.agency_id) : undefined,
        category_ids: formData.category_ids.map(id => parseInt(id)).filter(id => !isNaN(id)),
        tag_ids: formData.tag_ids.map(id => parseInt(id)).filter(id => !isNaN(id)),
        subject_ids: formData.subject_ids.map(id => parseInt(id)).filter(id => !isNaN(id)),
        publication_date: formData.publication_date || undefined,
        effective_date: formData.effective_date || undefined,
        termination_date: formData.termination_date || undefined,
        comment_due_date: formData.comment_due_date || undefined,
        public_hearing_date: formData.public_hearing_date || undefined,
      };

      await apiService.updateDocument(parseInt(documentId), updateData);
      setSuccess('Document updated successfully!');

      // Redirect to document detail page after a short delay
      setTimeout(() => {
        router.push(`/documents/${documentId}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update document. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (!isAuthenticated || user?.role === 'viewer') {
    return null;
  }

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-8 w-1/2"></div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="space-y-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i}>
                    <div className="h-4 bg-gray-200 rounded mb-2 w-1/4"></div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href={`/documents/${documentId}`}
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Document
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <PencilIcon className="h-8 w-8 mr-3 text-primary-600" />
            Edit Document
          </h1>
          <p className="text-gray-600">
            Update the document information and content
          </p>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6 flex items-center">
            <XMarkIcon className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-6 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            {success}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter document title"
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Type *
                </label>
                <select
                  id="type"
                  name="type"
                  required
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="rule">Rule</option>
                  <option value="proposed_rule">Proposed Rule</option>
                  <option value="notice">Notice</option>
                  <option value="presidential_document">Presidential Document</option>
                </select>
              </div>

              <div>
                <label htmlFor="agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency *
                </label>
                <select
                  id="agency_id"
                  name="agency_id"
                  required
                  value={formData.agency_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select an agency</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.name} ({agency.short_name})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categories
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                  {categories.map((category) => (
                    <label key={category.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.category_ids.includes(category.id.toString())}
                        onChange={() => handleCategoryChange(category.id.toString())}
                        className="mr-2"
                      />
                      {category.name}
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                  {tags.map((tag) => (
                    <label key={tag.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.tag_ids.includes(tag.id.toString())}
                        onChange={() => handleTagChange(tag.id.toString())}
                        className="mr-2"
                      />
                      {tag.name}
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subjects
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                  {subjects.map((subject) => (
                    <label key={subject.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.subject_ids.includes(subject.id.toString())}
                        onChange={() => handleSubjectChange(subject.id.toString())}
                        className="mr-2"
                      />
                      {subject.name}
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Abstract */}
            <div>
              <label htmlFor="abstract" className="block text-sm font-medium text-gray-700 mb-2">
                Abstract
              </label>
              <textarea
                id="abstract"
                name="abstract"
                rows={3}
                value={formData.abstract}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief abstract of the document..."
              />
            </div>

            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Content *
              </label>
              <textarea
                id="content"
                name="content"
                rows={12}
                required
                value={formData.content}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Full document content (supports Markdown)..."
              />
              <p className="mt-1 text-sm text-gray-500">
                You can use Markdown formatting for rich text content.
              </p>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href={`/documents/${documentId}`}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Updating...' : 'Update Document'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default EditDocumentPage;
