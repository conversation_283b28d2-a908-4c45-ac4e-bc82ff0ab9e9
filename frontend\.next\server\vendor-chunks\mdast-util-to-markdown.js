"use strict";
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   blockquote: () => (/* binding */ blockquote)
/* harmony export */ });
/**
 * @import {Blockquote, Parents} from 'mdast'
 * @import {Info, Map, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {Blockquote} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function blockquote(node, _, state, info) {
  const exit = state.enter('blockquote')
  const tracker = state.createTracker(info)
  tracker.move('> ')
  tracker.shift(2)
  const value = state.indentLines(
    state.containerFlow(node, tracker.current()),
    map
  )
  exit()
  return value
}

/** @type {Map} */
function map(line, _, blank) {
  return '>' + (blank ? '' : ' ') + line
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   hardBreak: () => (/* binding */ hardBreak)
/* harmony export */ });
/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js");
/**
 * @import {Break, Parents} from 'mdast'
 * @import {Info, State} from 'mdast-util-to-markdown'
 */



/**
 * @param {Break} _
 * @param {Parents | undefined} _1
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function hardBreak(_, _1, state, info) {
  let index = -1

  while (++index < state.unsafe.length) {
    // If we can’t put eols in this construct (setext headings, tables), use a
    // space instead.
    if (
      state.unsafe[index].character === '\n' &&
      (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])
    ) {
      return /[ \t]/.test(info.before) ? '' : ' '
    }
  }

  return '\\\n'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   code: () => (/* binding */ code)
/* harmony export */ });
/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ "(ssr)/./node_modules/longest-streak/index.js");
/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js");
/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js");
/**
 * @import {Info, Map, State} from 'mdast-util-to-markdown'
 * @import {Code, Parents} from 'mdast'
 */





/**
 * @param {Code} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function code(node, _, state, info) {
  const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state)
  const raw = node.value || ''
  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'

  if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {
    const exit = state.enter('codeIndented')
    const value = state.indentLines(raw, map)
    exit()
    return value
  }

  const tracker = state.createTracker(info)
  const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3))
  const exit = state.enter('codeFenced')
  let value = tracker.move(sequence)

  if (node.lang) {
    const subexit = state.enter(`codeFencedLang${suffix}`)
    value += tracker.move(
      state.safe(node.lang, {
        before: value,
        after: ' ',
        encode: ['`'],
        ...tracker.current()
      })
    )
    subexit()
  }

  if (node.lang && node.meta) {
    const subexit = state.enter(`codeFencedMeta${suffix}`)
    value += tracker.move(' ')
    value += tracker.move(
      state.safe(node.meta, {
        before: value,
        after: '\n',
        encode: ['`'],
        ...tracker.current()
      })
    )
    subexit()
  }

  value += tracker.move('\n')

  if (raw) {
    value += tracker.move(raw + '\n')
  }

  value += tracker.move(sequence)
  exit()
  return value
}

/** @type {Map} */
function map(line, _, blank) {
  return (blank ? '' : '    ') + line
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   definition: () => (/* binding */ definition)
/* harmony export */ });
/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Definition, Parents} from 'mdast'
 */



/**
 * @param {Definition} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function definition(node, _, state, info) {
  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)
  const suffix = quote === '"' ? 'Quote' : 'Apostrophe'
  const exit = state.enter('definition')
  let subexit = state.enter('label')
  const tracker = state.createTracker(info)
  let value = tracker.move('[')
  value += tracker.move(
    state.safe(state.associationId(node), {
      before: value,
      after: ']',
      ...tracker.current()
    })
  )
  value += tracker.move(']: ')

  subexit()

  if (
    // If there’s no url, or…
    !node.url ||
    // If there are control characters or whitespace.
    /[\0- \u007F]/.test(node.url)
  ) {
    subexit = state.enter('destinationLiteral')
    value += tracker.move('<')
    value += tracker.move(
      state.safe(node.url, {before: value, after: '>', ...tracker.current()})
    )
    value += tracker.move('>')
  } else {
    // No whitespace, raw is prettier.
    subexit = state.enter('destinationRaw')
    value += tracker.move(
      state.safe(node.url, {
        before: value,
        after: node.title ? ' ' : '\n',
        ...tracker.current()
      })
    )
  }

  subexit()

  if (node.title) {
    subexit = state.enter(`title${suffix}`)
    value += tracker.move(' ' + quote)
    value += tracker.move(
      state.safe(node.title, {
        before: value,
        after: quote,
        ...tracker.current()
      })
    )
    value += tracker.move(quote)
    subexit()
  }

  exit()

  return value
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   emphasis: () => (/* binding */ emphasis)
/* harmony export */ });
/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js");
/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js");
/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Emphasis, Parents} from 'mdast'
 */





emphasis.peek = emphasisPeek

/**
 * @param {Emphasis} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function emphasis(node, _, state, info) {
  const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state)
  const exit = state.enter('emphasis')
  const tracker = state.createTracker(info)
  const before = tracker.move(marker)

  let between = tracker.move(
    state.containerPhrasing(node, {
      after: marker,
      before,
      ...tracker.current()
    })
  )
  const betweenHead = between.charCodeAt(0)
  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(
    info.before.charCodeAt(info.before.length - 1),
    betweenHead,
    marker
  )

  if (open.inside) {
    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)
  }

  const betweenTail = between.charCodeAt(between.length - 1)
  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)

  if (close.inside) {
    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)
  }

  const after = tracker.move(marker)

  exit()

  state.attentionEncodeSurroundingInfo = {
    after: close.outside,
    before: open.outside
  }
  return before + between + after
}

/**
 * @param {Emphasis} _
 * @param {Parents | undefined} _1
 * @param {State} state
 * @returns {string}
 */
function emphasisPeek(_, _1, state) {
  return state.options.emphasis || '*'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   heading: () => (/* binding */ heading)
/* harmony export */ });
/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js");
/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Heading, Parents} from 'mdast'
 */




/**
 * @param {Heading} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function heading(node, _, state, info) {
  const rank = Math.max(Math.min(6, node.depth || 1), 1)
  const tracker = state.createTracker(info)

  if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {
    const exit = state.enter('headingSetext')
    const subexit = state.enter('phrasing')
    const value = state.containerPhrasing(node, {
      ...tracker.current(),
      before: '\n',
      after: '\n'
    })
    subexit()
    exit()

    return (
      value +
      '\n' +
      (rank === 1 ? '=' : '-').repeat(
        // The whole size…
        value.length -
          // Minus the position of the character after the last EOL (or
          // 0 if there is none)…
          (Math.max(value.lastIndexOf('\r'), value.lastIndexOf('\n')) + 1)
      )
    )
  }

  const sequence = '#'.repeat(rank)
  const exit = state.enter('headingAtx')
  const subexit = state.enter('phrasing')

  // Note: for proper tracking, we should reset the output positions when there
  // is no content returned, because then the space is not output.
  // Practically, in that case, there is no content, so it doesn’t matter that
  // we’ve tracked one too many characters.
  tracker.move(sequence + ' ')

  let value = state.containerPhrasing(node, {
    before: '# ',
    after: '\n',
    ...tracker.current()
  })

  if (/^[\t ]/.test(value)) {
    // To do: what effect has the character reference on tracking?
    value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1)
  }

  value = value ? sequence + ' ' + value : sequence

  if (state.options.closeAtx) {
    value += ' ' + sequence
  }

  subexit()
  exit()

  return value
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   html: () => (/* binding */ html)
/* harmony export */ });
/**
 * @import {Html} from 'mdast'
 */

html.peek = htmlPeek

/**
 * @param {Html} node
 * @returns {string}
 */
function html(node) {
  return node.value || ''
}

/**
 * @returns {string}
 */
function htmlPeek() {
  return '<'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   imageReference: () => (/* binding */ imageReference)
/* harmony export */ });
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {ImageReference, Parents} from 'mdast'
 */

imageReference.peek = imageReferencePeek

/**
 * @param {ImageReference} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function imageReference(node, _, state, info) {
  const type = node.referenceType
  const exit = state.enter('imageReference')
  let subexit = state.enter('label')
  const tracker = state.createTracker(info)
  let value = tracker.move('![')
  const alt = state.safe(node.alt, {
    before: value,
    after: ']',
    ...tracker.current()
  })
  value += tracker.move(alt + '][')

  subexit()
  // Hide the fact that we’re in phrasing, because escapes don’t work.
  const stack = state.stack
  state.stack = []
  subexit = state.enter('reference')
  // Note: for proper tracking, we should reset the output positions when we end
  // up making a `shortcut` reference, because then there is no brace output.
  // Practically, in that case, there is no content, so it doesn’t matter that
  // we’ve tracked one too many characters.
  const reference = state.safe(state.associationId(node), {
    before: value,
    after: ']',
    ...tracker.current()
  })
  subexit()
  state.stack = stack
  exit()

  if (type === 'full' || !alt || alt !== reference) {
    value += tracker.move(reference + ']')
  } else if (type === 'shortcut') {
    // Remove the unwanted `[`.
    value = value.slice(0, -1)
  } else {
    value += tracker.move(']')
  }

  return value
}

/**
 * @returns {string}
 */
function imageReferencePeek() {
  return '!'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   image: () => (/* binding */ image)
/* harmony export */ });
/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Image, Parents} from 'mdast'
 */



image.peek = imagePeek

/**
 * @param {Image} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function image(node, _, state, info) {
  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)
  const suffix = quote === '"' ? 'Quote' : 'Apostrophe'
  const exit = state.enter('image')
  let subexit = state.enter('label')
  const tracker = state.createTracker(info)
  let value = tracker.move('![')
  value += tracker.move(
    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})
  )
  value += tracker.move('](')

  subexit()

  if (
    // If there’s no url but there is a title…
    (!node.url && node.title) ||
    // If there are control characters or whitespace.
    /[\0- \u007F]/.test(node.url)
  ) {
    subexit = state.enter('destinationLiteral')
    value += tracker.move('<')
    value += tracker.move(
      state.safe(node.url, {before: value, after: '>', ...tracker.current()})
    )
    value += tracker.move('>')
  } else {
    // No whitespace, raw is prettier.
    subexit = state.enter('destinationRaw')
    value += tracker.move(
      state.safe(node.url, {
        before: value,
        after: node.title ? ' ' : ')',
        ...tracker.current()
      })
    )
  }

  subexit()

  if (node.title) {
    subexit = state.enter(`title${suffix}`)
    value += tracker.move(' ' + quote)
    value += tracker.move(
      state.safe(node.title, {
        before: value,
        after: quote,
        ...tracker.current()
      })
    )
    value += tracker.move(quote)
    subexit()
  }

  value += tracker.move(')')
  exit()

  return value
}

/**
 * @returns {string}
 */
function imagePeek() {
  return '!'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   handle: () => (/* binding */ handle)
/* harmony export */ });
/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js");
/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js");
/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js");
/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js");
/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js");
/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js");
/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js");
/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js");
/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js");
/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js");
/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js");
/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js");
/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js");
/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js");
/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js");
/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js");
/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js");
/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js");
/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js");




















/**
 * Default (CommonMark) handlers.
 */
const handle = {
  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,
  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,
  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,
  definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,
  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,
  hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,
  heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,
  html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,
  image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,
  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,
  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,
  link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,
  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,
  list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,
  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,
  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,
  root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,
  strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,
  text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,
  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   inlineCode: () => (/* binding */ inlineCode)
/* harmony export */ });
/**
 * @import {State} from 'mdast-util-to-markdown'
 * @import {InlineCode, Parents} from 'mdast'
 */

inlineCode.peek = inlineCodePeek

/**
 * @param {InlineCode} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @returns {string}
 */
function inlineCode(node, _, state) {
  let value = node.value || ''
  let sequence = '`'
  let index = -1

  // If there is a single grave accent on its own in the code, use a fence of
  // two.
  // If there are two in a row, use one.
  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {
    sequence += '`'
  }

  // If this is not just spaces or eols (tabs don’t count), and either the
  // first or last character are a space, eol, or tick, then pad with spaces.
  if (
    /[^ \r\n]/.test(value) &&
    ((/^[ \r\n]/.test(value) && /[ \r\n]$/.test(value)) || /^`|`$/.test(value))
  ) {
    value = ' ' + value + ' '
  }

  // We have a potential problem: certain characters after eols could result in
  // blocks being seen.
  // For example, if someone injected the string `'\n# b'`, then that would
  // result in an ATX heading.
  // We can’t escape characters in `inlineCode`, but because eols are
  // transformed to spaces when going from markdown to HTML anyway, we can swap
  // them out.
  while (++index < state.unsafe.length) {
    const pattern = state.unsafe[index]
    const expression = state.compilePattern(pattern)
    /** @type {RegExpExecArray | null} */
    let match

    // Only look for `atBreak`s.
    // Btw: note that `atBreak` patterns will always start the regex at LF or
    // CR.
    if (!pattern.atBreak) continue

    while ((match = expression.exec(value))) {
      let position = match.index

      // Support CRLF (patterns only look for one of the characters).
      if (
        value.charCodeAt(position) === 10 /* `\n` */ &&
        value.charCodeAt(position - 1) === 13 /* `\r` */
      ) {
        position--
      }

      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)
    }
  }

  return sequence + value + sequence
}

/**
 * @returns {string}
 */
function inlineCodePeek() {
  return '`'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   linkReference: () => (/* binding */ linkReference)
/* harmony export */ });
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {LinkReference, Parents} from 'mdast'
 */

linkReference.peek = linkReferencePeek

/**
 * @param {LinkReference} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function linkReference(node, _, state, info) {
  const type = node.referenceType
  const exit = state.enter('linkReference')
  let subexit = state.enter('label')
  const tracker = state.createTracker(info)
  let value = tracker.move('[')
  const text = state.containerPhrasing(node, {
    before: value,
    after: ']',
    ...tracker.current()
  })
  value += tracker.move(text + '][')

  subexit()
  // Hide the fact that we’re in phrasing, because escapes don’t work.
  const stack = state.stack
  state.stack = []
  subexit = state.enter('reference')
  // Note: for proper tracking, we should reset the output positions when we end
  // up making a `shortcut` reference, because then there is no brace output.
  // Practically, in that case, there is no content, so it doesn’t matter that
  // we’ve tracked one too many characters.
  const reference = state.safe(state.associationId(node), {
    before: value,
    after: ']',
    ...tracker.current()
  })
  subexit()
  state.stack = stack
  exit()

  if (type === 'full' || !text || text !== reference) {
    value += tracker.move(reference + ']')
  } else if (type === 'shortcut') {
    // Remove the unwanted `[`.
    value = value.slice(0, -1)
  } else {
    value += tracker.move(']')
  }

  return value
}

/**
 * @returns {string}
 */
function linkReferencePeek() {
  return '['
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   link: () => (/* binding */ link)
/* harmony export */ });
/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js");
/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Link, Parents} from 'mdast'
 * @import {Exit} from '../types.js'
 */




link.peek = linkPeek

/**
 * @param {Link} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function link(node, _, state, info) {
  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)
  const suffix = quote === '"' ? 'Quote' : 'Apostrophe'
  const tracker = state.createTracker(info)
  /** @type {Exit} */
  let exit
  /** @type {Exit} */
  let subexit

  if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {
    // Hide the fact that we’re in phrasing, because escapes don’t work.
    const stack = state.stack
    state.stack = []
    exit = state.enter('autolink')
    let value = tracker.move('<')
    value += tracker.move(
      state.containerPhrasing(node, {
        before: value,
        after: '>',
        ...tracker.current()
      })
    )
    value += tracker.move('>')
    exit()
    state.stack = stack
    return value
  }

  exit = state.enter('link')
  subexit = state.enter('label')
  let value = tracker.move('[')
  value += tracker.move(
    state.containerPhrasing(node, {
      before: value,
      after: '](',
      ...tracker.current()
    })
  )
  value += tracker.move('](')
  subexit()

  if (
    // If there’s no url but there is a title…
    (!node.url && node.title) ||
    // If there are control characters or whitespace.
    /[\0- \u007F]/.test(node.url)
  ) {
    subexit = state.enter('destinationLiteral')
    value += tracker.move('<')
    value += tracker.move(
      state.safe(node.url, {before: value, after: '>', ...tracker.current()})
    )
    value += tracker.move('>')
  } else {
    // No whitespace, raw is prettier.
    subexit = state.enter('destinationRaw')
    value += tracker.move(
      state.safe(node.url, {
        before: value,
        after: node.title ? ' ' : ')',
        ...tracker.current()
      })
    )
  }

  subexit()

  if (node.title) {
    subexit = state.enter(`title${suffix}`)
    value += tracker.move(' ' + quote)
    value += tracker.move(
      state.safe(node.title, {
        before: value,
        after: quote,
        ...tracker.current()
      })
    )
    value += tracker.move(quote)
    subexit()
  }

  value += tracker.move(')')

  exit()
  return value
}

/**
 * @param {Link} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @returns {string}
 */
function linkPeek(node, _, state) {
  return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? '<' : '['
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   listItem: () => (/* binding */ listItem)
/* harmony export */ });
/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js");
/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js");
/**
 * @import {Info, Map, State} from 'mdast-util-to-markdown'
 * @import {ListItem, Parents} from 'mdast'
 */




/**
 * @param {ListItem} node
 * @param {Parents | undefined} parent
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function listItem(node, parent, state, info) {
  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)
  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)

  // Add the marker value for ordered lists.
  if (parent && parent.type === 'list' && parent.ordered) {
    bullet =
      (typeof parent.start === 'number' && parent.start > -1
        ? parent.start
        : 1) +
      (state.options.incrementListMarker === false
        ? 0
        : parent.children.indexOf(node)) +
      bullet
  }

  let size = bullet.length + 1

  if (
    listItemIndent === 'tab' ||
    (listItemIndent === 'mixed' &&
      ((parent && parent.type === 'list' && parent.spread) || node.spread))
  ) {
    size = Math.ceil(size / 4) * 4
  }

  const tracker = state.createTracker(info)
  tracker.move(bullet + ' '.repeat(size - bullet.length))
  tracker.shift(size)
  const exit = state.enter('listItem')
  const value = state.indentLines(
    state.containerFlow(node, tracker.current()),
    map
  )
  exit()

  return value

  /** @type {Map} */
  function map(line, index, blank) {
    if (index) {
      return (blank ? '' : ' '.repeat(size)) + line
    }

    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   list: () => (/* binding */ list)
/* harmony export */ });
/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js");
/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js");
/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js");
/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {List, Parents} from 'mdast'
 */






/**
 * @param {List} node
 * @param {Parents | undefined} parent
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function list(node, parent, state, info) {
  const exit = state.enter('list')
  const bulletCurrent = state.bulletCurrent
  /** @type {string} */
  let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)
  /** @type {string} */
  const bulletOther = node.ordered
    ? bullet === '.'
      ? ')'
      : '.'
    : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state)
  let useDifferentMarker =
    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false

  if (!node.ordered) {
    const firstListItem = node.children ? node.children[0] : undefined

    // If there’s an empty first list item directly in two list items,
    // we have to use a different bullet:
    //
    // ```markdown
    // * - *
    // ```
    //
    // …because otherwise it would become one big thematic break.
    if (
      // Bullet could be used as a thematic break marker:
      (bullet === '*' || bullet === '-') &&
      // Empty first list item:
      firstListItem &&
      (!firstListItem.children || !firstListItem.children[0]) &&
      // Directly in two other list items:
      state.stack[state.stack.length - 1] === 'list' &&
      state.stack[state.stack.length - 2] === 'listItem' &&
      state.stack[state.stack.length - 3] === 'list' &&
      state.stack[state.stack.length - 4] === 'listItem' &&
      // That are each the first child.
      state.indexStack[state.indexStack.length - 1] === 0 &&
      state.indexStack[state.indexStack.length - 2] === 0 &&
      state.indexStack[state.indexStack.length - 3] === 0
    ) {
      useDifferentMarker = true
    }

    // If there’s a thematic break at the start of the first list item,
    // we have to use a different bullet:
    //
    // ```markdown
    // * ---
    // ```
    //
    // …because otherwise it would become one big thematic break.
    if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {
      let index = -1

      while (++index < node.children.length) {
        const item = node.children[index]

        if (
          item &&
          item.type === 'listItem' &&
          item.children &&
          item.children[0] &&
          item.children[0].type === 'thematicBreak'
        ) {
          useDifferentMarker = true
          break
        }
      }
    }
  }

  if (useDifferentMarker) {
    bullet = bulletOther
  }

  state.bulletCurrent = bullet
  const value = state.containerFlow(node, info)
  state.bulletLastUsed = bullet
  state.bulletCurrent = bulletCurrent
  exit()
  return value
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   paragraph: () => (/* binding */ paragraph)
/* harmony export */ });
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Paragraph, Parents} from 'mdast'
 */

/**
 * @param {Paragraph} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function paragraph(node, _, state, info) {
  const exit = state.enter('paragraph')
  const subexit = state.enter('phrasing')
  const value = state.containerPhrasing(node, info)
  subexit()
  exit()
  return value
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   root: () => (/* binding */ root)
/* harmony export */ });
/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ "(ssr)/./node_modules/mdast-util-phrasing/lib/index.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Parents, Root} from 'mdast'
 */



/**
 * @param {Root} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function root(node, _, state, info) {
  // Note: `html` nodes are ambiguous.
  const hasPhrasing = node.children.some(function (d) {
    return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d)
  })

  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow
  return container.call(state, node, info)
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   strong: () => (/* binding */ strong)
/* harmony export */ });
/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js");
/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js");
/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js");
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Parents, Strong} from 'mdast'
 */





strong.peek = strongPeek

/**
 * @param {Strong} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function strong(node, _, state, info) {
  const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state)
  const exit = state.enter('strong')
  const tracker = state.createTracker(info)
  const before = tracker.move(marker + marker)

  let between = tracker.move(
    state.containerPhrasing(node, {
      after: marker,
      before,
      ...tracker.current()
    })
  )
  const betweenHead = between.charCodeAt(0)
  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(
    info.before.charCodeAt(info.before.length - 1),
    betweenHead,
    marker
  )

  if (open.inside) {
    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)
  }

  const betweenTail = between.charCodeAt(between.length - 1)
  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)

  if (close.inside) {
    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)
  }

  const after = tracker.move(marker + marker)

  exit()

  state.attentionEncodeSurroundingInfo = {
    after: close.outside,
    before: open.outside
  }
  return before + between + after
}

/**
 * @param {Strong} _
 * @param {Parents | undefined} _1
 * @param {State} state
 * @returns {string}
 */
function strongPeek(_, _1, state) {
  return state.options.strong || '*'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   text: () => (/* binding */ text)
/* harmony export */ });
/**
 * @import {Info, State} from 'mdast-util-to-markdown'
 * @import {Parents, Text} from 'mdast'
 */

/**
 * @param {Text} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
function text(node, _, state, info) {
  return state.safe(node.value, info)
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)
/* harmony export */ });
/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js");
/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js");
/**
 * @import {State} from 'mdast-util-to-markdown'
 * @import {Parents, ThematicBreak} from 'mdast'
 */




/**
 * @param {ThematicBreak} _
 * @param {Parents | undefined} _1
 * @param {State} state
 * @returns {string}
 */
function thematicBreak(_, _1, state) {
  const value = (
    (0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? ' ' : '')
  ).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state))

  return state.options.ruleSpaces ? value.slice(0, -1) : value
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['bulletOrdered'], null | undefined>}
 */
function checkBulletOrdered(state) {
  const marker = state.options.bulletOrdered || '.'

  if (marker !== '.' && marker !== ')') {
    throw new Error(
      'Cannot serialize items with `' +
        marker +
        '` for `options.bulletOrdered`, expected `.` or `)`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)
/* harmony export */ });
/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js");
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */



/**
 * @param {State} state
 * @returns {Exclude<Options['bullet'], null | undefined>}
 */
function checkBulletOther(state) {
  const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state)
  const bulletOther = state.options.bulletOther

  if (!bulletOther) {
    return bullet === '*' ? '-' : '*'
  }

  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {
    throw new Error(
      'Cannot serialize items with `' +
        bulletOther +
        '` for `options.bulletOther`, expected `*`, `+`, or `-`'
    )
  }

  if (bulletOther === bullet) {
    throw new Error(
      'Expected `bullet` (`' +
        bullet +
        '`) and `bulletOther` (`' +
        bulletOther +
        '`) to be different'
    )
  }

  return bulletOther
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkBullet: () => (/* binding */ checkBullet)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['bullet'], null | undefined>}
 */
function checkBullet(state) {
  const marker = state.options.bullet || '*'

  if (marker !== '*' && marker !== '+' && marker !== '-') {
    throw new Error(
      'Cannot serialize items with `' +
        marker +
        '` for `options.bullet`, expected `*`, `+`, or `-`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['emphasis'], null | undefined>}
 */
function checkEmphasis(state) {
  const marker = state.options.emphasis || '*'

  if (marker !== '*' && marker !== '_') {
    throw new Error(
      'Cannot serialize emphasis with `' +
        marker +
        '` for `options.emphasis`, expected `*`, or `_`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkFence: () => (/* binding */ checkFence)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['fence'], null | undefined>}
 */
function checkFence(state) {
  const marker = state.options.fence || '`'

  if (marker !== '`' && marker !== '~') {
    throw new Error(
      'Cannot serialize code with `' +
        marker +
        '` for `options.fence`, expected `` ` `` or `~`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['listItemIndent'], null | undefined>}
 */
function checkListItemIndent(state) {
  const style = state.options.listItemIndent || 'one'

  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {
    throw new Error(
      'Cannot serialize items with `' +
        style +
        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'
    )
  }

  return style
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkQuote: () => (/* binding */ checkQuote)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['quote'], null | undefined>}
 */
function checkQuote(state) {
  const marker = state.options.quote || '"'

  if (marker !== '"' && marker !== "'") {
    throw new Error(
      'Cannot serialize title with `' +
        marker +
        '` for `options.quote`, expected `"`, or `\'`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['ruleRepetition'], null | undefined>}
 */
function checkRuleRepetition(state) {
  const repetition = state.options.ruleRepetition || 3

  if (repetition < 3) {
    throw new Error(
      'Cannot serialize rules with repetition `' +
        repetition +
        '` for `options.ruleRepetition`, expected `3` or more'
    )
  }

  return repetition
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkRule: () => (/* binding */ checkRule)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['rule'], null | undefined>}
 */
function checkRule(state) {
  const marker = state.options.rule || '*'

  if (marker !== '*' && marker !== '-' && marker !== '_') {
    throw new Error(
      'Cannot serialize rules with `' +
        marker +
        '` for `options.rule`, expected `*`, `-`, or `_`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkStrong: () => (/* binding */ checkStrong)
/* harmony export */ });
/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */

/**
 * @param {State} state
 * @returns {Exclude<Options['strong'], null | undefined>}
 */
function checkStrong(state) {
  const marker = state.options.strong || '*'

  if (marker !== '*' && marker !== '_') {
    throw new Error(
      'Cannot serialize strong with `' +
        marker +
        '` for `options.strong`, expected `*`, or `_`'
    )
  }

  return marker
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)
/* harmony export */ });
/**
 * Encode a code point as a character reference.
 *
 * @param {number} code
 *   Code point to encode.
 * @returns {string}
 *   Encoded character reference.
 */
function encodeCharacterReference(code) {
  return '&#x' + code.toString(16).toUpperCase() + ';'
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)
/* harmony export */ });
/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ "(ssr)/./node_modules/micromark-util-classify-character/dev/index.js");
/**
 * @import {EncodeSides} from '../types.js'
 */



/**
 * Check whether to encode (as a character reference) the characters
 * surrounding an attention run.
 *
 * Which characters are around an attention run influence whether it works or
 * not.
 *
 * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.
 * See this markdown in a particular renderer to see what works:
 *
 * ```markdown
 * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |
 * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |
 * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |
 * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |
 * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |
 * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |
 * ```
 *
 * @param {number} outside
 *   Code point on the outer side of the run.
 * @param {number} inside
 *   Code point on the inner side of the run.
 * @param {'*' | '_'} marker
 *   Marker of the run.
 *   Underscores are handled more strictly (they form less often) than
 *   asterisks.
 * @returns {EncodeSides}
 *   Whether to encode characters.
 */
// Important: punctuation must never be encoded.
// Punctuation is solely used by markdown constructs.
// And by encoding itself.
// Encoding them will break constructs or double encode things.
function encodeInfo(outside, inside, marker) {
  const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside)
  const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside)

  // Letter outside:
  if (outsideKind === undefined) {
    return insideKind === undefined
      ? // Letter inside:
        // we have to encode *both* letters for `_` as it is looser.
        // it already forms for `*` (and GFMs `~`).
        marker === '_'
        ? {inside: true, outside: true}
        : {inside: false, outside: false}
      : insideKind === 1
        ? // Whitespace inside: encode both (letter, whitespace).
          {inside: true, outside: true}
        : // Punctuation inside: encode outer (letter)
          {inside: false, outside: true}
  }

  // Whitespace outside:
  if (outsideKind === 1) {
    return insideKind === undefined
      ? // Letter inside: already forms.
        {inside: false, outside: false}
      : insideKind === 1
        ? // Whitespace inside: encode both (whitespace).
          {inside: true, outside: true}
        : // Punctuation inside: already forms.
          {inside: false, outside: false}
  }

  // Punctuation outside:
  return insideKind === undefined
    ? // Letter inside: already forms.
      {inside: false, outside: false}
    : insideKind === 1
      ? // Whitespace inside: encode inner (whitespace).
        {inside: true, outside: false}
      : // Punctuation inside: already forms.
        {inside: false, outside: false}
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)
/* harmony export */ });
/**
 * @import {State} from 'mdast-util-to-markdown'
 * @import {Code} from 'mdast'
 */

/**
 * @param {Code} node
 * @param {State} state
 * @returns {boolean}
 */
function formatCodeAsIndented(node, state) {
  return Boolean(
    state.options.fences === false &&
      node.value &&
      // If there’s no info…
      !node.lang &&
      // And there’s a non-whitespace character…
      /[^ \r\n]/.test(node.value) &&
      // And the value doesn’t start or end in a blank…
      !/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(node.value)
  )
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)
/* harmony export */ });
/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ "(ssr)/./node_modules/unist-util-visit/lib/index.js");
/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ "(ssr)/./node_modules/unist-util-visit-parents/lib/index.js");
/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ "(ssr)/./node_modules/mdast-util-to-string/lib/index.js");
/**
 * @import {State} from 'mdast-util-to-markdown'
 * @import {Heading} from 'mdast'
 */




/**
 * @param {Heading} node
 * @param {State} state
 * @returns {boolean}
 */
function formatHeadingAsSetext(node, state) {
  let literalWithBreak = false

  // Look for literals with a line break.
  // Note that this also
  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function (node) {
    if (
      ('value' in node && /\r?\n|\r/.test(node.value)) ||
      node.type === 'break'
    ) {
      literalWithBreak = true
      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT
    }
  })

  return Boolean(
    (!node.depth || node.depth < 3) &&
      (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) &&
      (state.options.setext || literalWithBreak)
  )
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)
/* harmony export */ });
/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ "(ssr)/./node_modules/mdast-util-to-string/lib/index.js");
/**
 * @import {State} from 'mdast-util-to-markdown'
 * @import {Link} from 'mdast'
 */



/**
 * @param {Link} node
 * @param {State} state
 * @returns {boolean}
 */
function formatLinkAsAutolink(node, state) {
  const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node)

  return Boolean(
    !state.options.resourceLink &&
      // If there’s a url…
      node.url &&
      // And there’s a no title…
      !node.title &&
      // And the content of `node` is a single text node…
      node.children &&
      node.children.length === 1 &&
      node.children[0].type === 'text' &&
      // And if the url is the same as the content…
      (raw === node.url || 'mailto:' + raw === node.url) &&
      // And that starts w/ a protocol…
      /^[a-z][a-z+.-]+:/i.test(node.url) &&
      // And that doesn’t contain ASCII control codes (character escapes and
      // references don’t work), space, or angle brackets…
      !/[\0- <>\u007F]/.test(node.url)
  )
}


/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   patternInScope: () => (/* binding */ patternInScope)
/* harmony export */ });
/**
 * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'
 */

/**
 * @param {Array<ConstructName>} stack
 * @param {Unsafe} pattern
 * @returns {boolean}
 */
function patternInScope(stack, pattern) {
  return (
    listInScope(stack, pattern.inConstruct, true) &&
    !listInScope(stack, pattern.notInConstruct, false)
  )
}

/**
 * @param {Array<ConstructName>} stack
 * @param {Unsafe['inConstruct']} list
 * @param {boolean} none
 * @returns {boolean}
 */
function listInScope(stack, list, none) {
  if (typeof list === 'string') {
    list = [list]
  }

  if (!list || list.length === 0) {
    return none
  }

  let index = -1

  while (++index < list.length) {
    if (stack.includes(list[index])) {
      return true
    }
  }

  return false
}


/***/ })

};
;