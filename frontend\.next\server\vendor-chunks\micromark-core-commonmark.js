"use strict";
exports.id = "vendor-chunks/micromark-core-commonmark";
exports.ids = ["vendor-chunks/micromark-core-commonmark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/attention.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   attention: () => (/* binding */ attention)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-chunked */ "(ssr)/./node_modules/micromark-util-chunked/dev/index.js");
/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-classify-character */ "(ssr)/./node_modules/micromark-util-classify-character/dev/index.js");
/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ "(ssr)/./node_modules/micromark-util-resolve-all/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   Event,
 *   Point,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer,
 *   Token
 * } from 'micromark-util-types'
 */







/** @type {Construct} */
const attention = {
  name: 'attention',
  resolveAll: resolveAllAttention,
  tokenize: tokenizeAttention
}

/**
 * Take all events and resolve attention to emphasis or strong.
 *
 * @type {Resolver}
 */
// eslint-disable-next-line complexity
function resolveAllAttention(events, context) {
  let index = -1
  /** @type {number} */
  let open
  /** @type {Token} */
  let group
  /** @type {Token} */
  let text
  /** @type {Token} */
  let openingSequence
  /** @type {Token} */
  let closingSequence
  /** @type {number} */
  let use
  /** @type {Array<Event>} */
  let nextEvents
  /** @type {number} */
  let offset

  // Walk through all events.
  //
  // Note: performance of this is fine on an mb of normal markdown, but it’s
  // a bottleneck for malicious stuff.
  while (++index < events.length) {
    // Find a token that can close.
    if (
      events[index][0] === 'enter' &&
      events[index][1].type === 'attentionSequence' &&
      events[index][1]._close
    ) {
      open = index

      // Now walk back to find an opener.
      while (open--) {
        // Find a token that can open the closer.
        if (
          events[open][0] === 'exit' &&
          events[open][1].type === 'attentionSequence' &&
          events[open][1]._open &&
          // If the markers are the same:
          context.sliceSerialize(events[open][1]).charCodeAt(0) ===
            context.sliceSerialize(events[index][1]).charCodeAt(0)
        ) {
          // If the opening can close or the closing can open,
          // and the close size *is not* a multiple of three,
          // but the sum of the opening and closing size *is* multiple of three,
          // then don’t match.
          if (
            (events[open][1]._close || events[index][1]._open) &&
            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&
            !(
              (events[open][1].end.offset -
                events[open][1].start.offset +
                events[index][1].end.offset -
                events[index][1].start.offset) %
              3
            )
          ) {
            continue
          }

          // Number of markers to use from the sequence.
          use =
            events[open][1].end.offset - events[open][1].start.offset > 1 &&
            events[index][1].end.offset - events[index][1].start.offset > 1
              ? 2
              : 1

          const start = {...events[open][1].end}
          const end = {...events[index][1].start}
          movePoint(start, -use)
          movePoint(end, use)

          openingSequence = {
            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strongSequence : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasisSequence,
            start,
            end: {...events[open][1].end}
          }
          closingSequence = {
            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strongSequence : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasisSequence,
            start: {...events[index][1].start},
            end
          }
          text = {
            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strongText : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasisText,
            start: {...events[open][1].end},
            end: {...events[index][1].start}
          }
          group = {
            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strong : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasis,
            start: {...openingSequence.start},
            end: {...closingSequence.end}
          }

          events[open][1].end = {...openingSequence.start}
          events[index][1].start = {...closingSequence.end}

          nextEvents = []

          // If there are more markers in the opening, add them before.
          if (events[open][1].end.offset - events[open][1].start.offset) {
            nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [
              ['enter', events[open][1], context],
              ['exit', events[open][1], context]
            ])
          }

          // Opening.
          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [
            ['enter', group, context],
            ['enter', openingSequence, context],
            ['exit', openingSequence, context],
            ['enter', text, context]
          ])

          // Always populated by defaults.
          ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(
            context.parser.constructs.insideSpan.null,
            'expected `insideSpan` to be populated'
          )

          // Between.
          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(
            nextEvents,
            (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(
              context.parser.constructs.insideSpan.null,
              events.slice(open + 1, index),
              context
            )
          )

          // Closing.
          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [
            ['exit', text, context],
            ['enter', closingSequence, context],
            ['exit', closingSequence, context],
            ['exit', group, context]
          ])

          // If there are more markers in the closing, add them after.
          if (events[index][1].end.offset - events[index][1].start.offset) {
            offset = 2
            nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [
              ['enter', events[index][1], context],
              ['exit', events[index][1], context]
            ])
          } else {
            offset = 0
          }

          (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, open - 1, index - open + 3, nextEvents)

          index = open + nextEvents.length - offset - 2
          break
        }
      }
    }
  }

  // Remove remaining sequences.
  index = -1

  while (++index < events.length) {
    if (events[index][1].type === 'attentionSequence') {
      events[index][1].type = 'data'
    }
  }

  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeAttention(effects, ok) {
  const attentionMarkers = this.parser.constructs.attentionMarkers.null
  const previous = this.previous
  const before = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__.classifyCharacter)(previous)

  /** @type {NonNullable<Code>} */
  let marker

  return start

  /**
   * Before a sequence.
   *
   * ```markdown
   * > | **
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.underscore,
      'expected asterisk or underscore'
    )
    marker = code
    effects.enter('attentionSequence')
    return inside(code)
  }

  /**
   * In a sequence.
   *
   * ```markdown
   * > | **
   *     ^^
   * ```
   *
   * @type {State}
   */
  function inside(code) {
    if (code === marker) {
      effects.consume(code)
      return inside
    }

    const token = effects.exit('attentionSequence')

    // To do: next major: move this to resolver, just like `markdown-rs`.
    const after = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__.classifyCharacter)(code)

    // Always populated by defaults.
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(attentionMarkers, 'expected `attentionMarkers` to be populated')

    const open =
      !after ||
      (after === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.constants.characterGroupPunctuation && before) ||
      attentionMarkers.includes(code)
    const close =
      !before ||
      (before === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.constants.characterGroupPunctuation && after) ||
      attentionMarkers.includes(previous)

    token._open = Boolean(
      marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk ? open : open && (before || !close)
    )
    token._close = Boolean(
      marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk ? close : close && (after || !open)
    )
    return ok(code)
  }
}

/**
 * Move a point a bit.
 *
 * Note: `move` only works inside lines! It’s not possible to move past other
 * chunks (replacement characters, tabs, or line endings).
 *
 * @param {Point} point
 *   Point.
 * @param {number} offset
 *   Amount to move.
 * @returns {undefined}
 *   Nothing.
 */
function movePoint(point, offset) {
  point.column += offset
  point.offset += offset
  point._bufferIndex += offset
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/autolink.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   autolink: () => (/* binding */ autolink)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const autolink = {name: 'autolink', tokenize: tokenizeAutolink}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeAutolink(effects, ok, nok) {
  let size = 0

  return start

  /**
   * Start of an autolink.
   *
   * ```markdown
   * > | a<https://example.com>b
   *      ^
   * > | a<<EMAIL>>b
   *      ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolink)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol)
    return open
  }

  /**
   * After `<`, at protocol or atext.
   *
   * ```markdown
   * > | a<https://example.com>b
   *       ^
   * > | a<<EMAIL>>b
   *       ^
   * ```
   *
   * @type {State}
   */
  function open(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {
      effects.consume(code)
      return schemeOrEmailAtext
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.atSign) {
      return nok(code)
    }

    return emailAtext(code)
  }

  /**
   * At second byte of protocol or atext.
   *
   * ```markdown
   * > | a<https://example.com>b
   *        ^
   * > | a<<EMAIL>>b
   *        ^
   * ```
   *
   * @type {State}
   */
  function schemeOrEmailAtext(code) {
    // ASCII alphanumeric and `+`, `-`, and `.`.
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)
    ) {
      // Count the previous alphabetical from `open` too.
      size = 1
      return schemeInsideOrEmailAtext(code)
    }

    return emailAtext(code)
  }

  /**
   * In ambiguous protocol or atext.
   *
   * ```markdown
   * > | a<https://example.com>b
   *        ^
   * > | a<<EMAIL>>b
   *        ^
   * ```
   *
   * @type {State}
   */
  function schemeInsideOrEmailAtext(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {
      effects.consume(code)
      size = 0
      return urlInside
    }

    // ASCII alphanumeric and `+`, `-`, and `.`.
    if (
      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||
        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||
        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||
        (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) &&
      size++ < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.autolinkSchemeSizeMax
    ) {
      effects.consume(code)
      return schemeInsideOrEmailAtext
    }

    size = 0
    return emailAtext(code)
  }

  /**
   * After protocol, in URL.
   *
   * ```markdown
   * > | a<https://example.com>b
   *             ^
   * ```
   *
   * @type {State}
   */
  function urlInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol)
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolink)
      return ok
    }

    // ASCII control, space, or `<`.
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiControl)(code)
    ) {
      return nok(code)
    }

    effects.consume(code)
    return urlInside
  }

  /**
   * In email atext.
   *
   * ```markdown
   * > | a<<EMAIL>>b
   *              ^
   * ```
   *
   * @type {State}
   */
  function emailAtext(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.atSign) {
      effects.consume(code)
      return emailAtSignOrDot
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAtext)(code)) {
      effects.consume(code)
      return emailAtext
    }

    return nok(code)
  }

  /**
   * In label, after at-sign or dot.
   *
   * ```markdown
   * > | a<<EMAIL>>b
   *                 ^       ^
   * ```
   *
   * @type {State}
   */
  function emailAtSignOrDot(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code) ? emailLabel(code) : nok(code)
  }

  /**
   * In label, where `.` and `>` are allowed.
   *
   * ```markdown
   * > | a<<EMAIL>>b
   *                   ^
   * ```
   *
   * @type {State}
   */
  function emailLabel(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot) {
      effects.consume(code)
      size = 0
      return emailAtSignOrDot
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {
      // Exit, then change the token type.
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol).type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkEmail
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolink)
      return ok
    }

    return emailValue(code)
  }

  /**
   * In label, where `.` and `>` are *not* allowed.
   *
   * Though, this is also used in `emailLabel` to parse other values.
   *
   * ```markdown
   * > | a<<EMAIL>>b
   *                    ^
   * ```
   *
   * @type {State}
   */
  function emailValue(code) {
    // ASCII alphanumeric or `-`.
    if (
      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) &&
      size++ < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.autolinkDomainSizeMax
    ) {
      const next = code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ? emailValue : emailLabel
      effects.consume(code)
      return next
    }

    return nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/blank-line.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   blankLine: () => (/* binding */ blankLine)
/* harmony export */ });
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const blankLine = {partial: true, tokenize: tokenizeBlankLine}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeBlankLine(effects, ok, nok) {
  return start

  /**
   * Start of blank line.
   *
   * > 👉 **Note**: `␠` represents a space character.
   *
   * ```markdown
   * > | ␠␠␊
   *     ^
   * > | ␊
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix)(code)
      : after(code)
  }

  /**
   * At eof/eol, after optional whitespace.
   *
   * > 👉 **Note**: `␠` represents a space character.
   *
   * ```markdown
   * > | ␠␠␊
   *       ^
   * > | ␊
   *     ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownLineEnding)(code) ? ok(code) : nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/block-quote.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   blockQuote: () => (/* binding */ blockQuote)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Construct,
 *   Exiter,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const blockQuote = {
  continuation: {tokenize: tokenizeBlockQuoteContinuation},
  exit,
  name: 'blockQuote',
  tokenize: tokenizeBlockQuoteStart
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeBlockQuoteStart(effects, ok, nok) {
  const self = this

  return start

  /**
   * Start of block quote.
   *
   * ```markdown
   * > | > a
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan) {
      const state = self.containerState

      ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(state, 'expected `containerState` to be defined in container')

      if (!state.open) {
        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuote, {_container: true})
        state.open = true
      }

      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuoteMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuoteMarker)
      return after
    }

    return nok(code)
  }

  /**
   * After `>`, before optional whitespace.
   *
   * ```markdown
   * > | > a
   *      ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefixWhitespace)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefixWhitespace)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)
      return ok
    }

    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)
    return ok(code)
  }
}

/**
 * Start of block quote continuation.
 *
 * ```markdown
 *   | > a
 * > | > b
 *     ^
 * ```
 *
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeBlockQuoteContinuation(effects, ok, nok) {
  const self = this

  return contStart

  /**
   * Start of block quote continuation.
   *
   * Also used to parse the first block quote opening.
   *
   * ```markdown
   *   | > a
   * > | > b
   *     ^
   * ```
   *
   * @type {State}
   */
  function contStart(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      // Always populated by defaults.
      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
        self.parser.constructs.disable.null,
        'expected `disable.null` to be populated'
      )

      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(
        effects,
        contBefore,
        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,
        self.parser.constructs.disable.null.includes('codeIndented')
          ? undefined
          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize
      )(code)
    }

    return contBefore(code)
  }

  /**
   * At `>`, after optional whitespace.
   *
   * Also used to parse the first block quote opening.
   *
   * ```markdown
   *   | > a
   * > | > b
   *     ^
   * ```
   *
   * @type {State}
   */
  function contBefore(code) {
    return effects.attempt(blockQuote, ok, nok)(code)
  }
}

/** @type {Exiter} */
function exit(effects) {
  effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuote)
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/character-escape.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   characterEscape: () => (/* binding */ characterEscape)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const characterEscape = {
  name: 'characterEscape',
  tokenize: tokenizeCharacterEscape
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeCharacterEscape(effects, ok, nok) {
  return start

  /**
   * Start of character escape.
   *
   * ```markdown
   * > | a\*b
   *      ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash, 'expected `\\`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscape)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.escapeMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.escapeMarker)
    return inside
  }

  /**
   * After `\`, at punctuation.
   *
   * ```markdown
   * > | a\*b
   *       ^
   * ```
   *
   * @type {State}
   */
  function inside(code) {
    // ASCII punctuation.
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiPunctuation)(code)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscapeValue)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscapeValue)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscape)
      return ok
    }

    return nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/character-reference.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   characterReference: () => (/* binding */ characterReference)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! decode-named-character-reference */ "(ssr)/./node_modules/decode-named-character-reference/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const characterReference = {
  name: 'characterReference',
  tokenize: tokenizeCharacterReference
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeCharacterReference(effects, ok, nok) {
  const self = this
  let size = 0
  /** @type {number} */
  let max
  /** @type {(code: Code) => boolean} */
  let test

  return start

  /**
   * Start of character reference.
   *
   * ```markdown
   * > | a&amp;b
   *      ^
   * > | a&#123;b
   *      ^
   * > | a&#x9;b
   *      ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.ampersand, 'expected `&`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReference)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)
    return open
  }

  /**
   * After `&`, at `#` for numeric references or alphanumeric for named
   * references.
   *
   * ```markdown
   * > | a&amp;b
   *       ^
   * > | a&#123;b
   *       ^
   * > | a&#x9;b
   *       ^
   * ```
   *
   * @type {State}
   */
  function open(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.numberSign) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerNumeric)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerNumeric)
      return numeric
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)
    max = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceNamedSizeMax
    test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric
    return value(code)
  }

  /**
   * After `#`, at `x` for hexadecimals or digit for decimals.
   *
   * ```markdown
   * > | a&#123;b
   *        ^
   * > | a&#x9;b
   *        ^
   * ```
   *
   * @type {State}
   */
  function numeric(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.uppercaseX || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lowercaseX) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerHexadecimal)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerHexadecimal)
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)
      max = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceHexadecimalSizeMax
      test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiHexDigit
      return value
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)
    max = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceDecimalSizeMax
    test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiDigit
    return value(code)
  }

  /**
   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.
   *
   * The character reference kind defines what and how many characters are
   * allowed.
   *
   * ```markdown
   * > | a&amp;b
   *       ^^^
   * > | a&#123;b
   *        ^^^
   * > | a&#x9;b
   *         ^
   * ```
   *
   * @type {State}
   */
  function value(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.semicolon && size) {
      const token = effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)

      if (
        test === micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric &&
        !(0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_5__.decodeNamedCharacterReference)(self.sliceSerialize(token))
      ) {
        return nok(code)
      }

      // To do: `markdown-rs` uses a different name:
      // `CharacterReferenceMarkerSemi`.
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReference)
      return ok
    }

    if (test(code) && size++ < max) {
      effects.consume(code)
      return value
    }

    return nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   codeFenced: () => (/* binding */ codeFenced)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const nonLazyContinuation = {
  partial: true,
  tokenize: tokenizeNonLazyContinuation
}

/** @type {Construct} */
const codeFenced = {
  concrete: true,
  name: 'codeFenced',
  tokenize: tokenizeCodeFenced
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeCodeFenced(effects, ok, nok) {
  const self = this
  /** @type {Construct} */
  const closeStart = {partial: true, tokenize: tokenizeCloseStart}
  let initialPrefix = 0
  let sizeOpen = 0
  /** @type {NonNullable<Code>} */
  let marker

  return start

  /**
   * Start of code.
   *
   * ```markdown
   * > | ~~~js
   *     ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function start(code) {
    // To do: parse whitespace like `markdown-rs`.
    return beforeSequenceOpen(code)
  }

  /**
   * In opening fence, after prefix, at sequence.
   *
   * ```markdown
   * > | ~~~js
   *     ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function beforeSequenceOpen(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.tilde,
      'expected `` ` `` or `~`'
    )

    const tail = self.events[self.events.length - 1]
    initialPrefix =
      tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix
        ? tail[2].sliceSerialize(tail[1], true).length
        : 0

    marker = code
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFenced)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)
    return sequenceOpen(code)
  }

  /**
   * In opening fence sequence.
   *
   * ```markdown
   * > | ~~~js
   *      ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function sequenceOpen(code) {
    if (code === marker) {
      sizeOpen++
      effects.consume(code)
      return sequenceOpen
    }

    if (sizeOpen < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.codeFencedSequenceSizeMin) {
      return nok(code)
    }

    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, infoBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)
      : infoBefore(code)
  }

  /**
   * In opening fence, after the sequence (and optional whitespace), before info.
   *
   * ```markdown
   * > | ~~~js
   *        ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function infoBefore(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)
      return self.interrupt
        ? ok(code)
        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.contentTypeString})
    return info(code)
  }

  /**
   * In info.
   *
   * ```markdown
   * > | ~~~js
   *        ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function info(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)
      return infoBefore(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)
      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, metaBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent && code === marker) {
      return nok(code)
    }

    effects.consume(code)
    return info
  }

  /**
   * In opening fence, after info and whitespace, before meta.
   *
   * ```markdown
   * > | ~~~js eval
   *           ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function metaBefore(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      return infoBefore(code)
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceMeta)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.contentTypeString})
    return meta(code)
  }

  /**
   * In meta.
   *
   * ```markdown
   * > | ~~~js eval
   *           ^
   *   | alert(1)
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function meta(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceMeta)
      return infoBefore(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent && code === marker) {
      return nok(code)
    }

    effects.consume(code)
    return meta
  }

  /**
   * At eol/eof in code, before a non-lazy closing fence or content.
   *
   * ```markdown
   * > | ~~~js
   *          ^
   * > | alert(1)
   *             ^
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function atNonLazyBreak(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')
    return effects.attempt(closeStart, after, contentBefore)(code)
  }

  /**
   * Before code content, not a closing fence, at eol.
   *
   * ```markdown
   *   | ~~~js
   * > | alert(1)
   *             ^
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function contentBefore(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    return contentStart
  }

  /**
   * Before code content, not a closing fence.
   *
   * ```markdown
   *   | ~~~js
   * > | alert(1)
   *     ^
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function contentStart(code) {
    return initialPrefix > 0 && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(
          effects,
          beforeContentChunk,
          micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,
          initialPrefix + 1
        )(code)
      : beforeContentChunk(code)
  }

  /**
   * Before code content, after optional prefix.
   *
   * ```markdown
   *   | ~~~js
   * > | alert(1)
   *     ^
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function beforeContentChunk(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)
    return contentChunk(code)
  }

  /**
   * In code content.
   *
   * ```markdown
   *   | ~~~js
   * > | alert(1)
   *     ^^^^^^^^
   *   | ~~~
   * ```
   *
   * @type {State}
   */
  function contentChunk(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)
      return beforeContentChunk(code)
    }

    effects.consume(code)
    return contentChunk
  }

  /**
   * After code.
   *
   * ```markdown
   *   | ~~~js
   *   | alert(1)
   * > | ~~~
   *        ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFenced)
    return ok(code)
  }

  /**
   * @this {TokenizeContext}
   *   Context.
   * @type {Tokenizer}
   */
  function tokenizeCloseStart(effects, ok, nok) {
    let size = 0

    return startBefore

    /**
     *
     *
     * @type {State}
     */
    function startBefore(code) {
      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
      return start
    }

    /**
     * Before closing fence, at optional whitespace.
     *
     * ```markdown
     *   | ~~~js
     *   | alert(1)
     * > | ~~~
     *     ^
     * ```
     *
     * @type {State}
     */
    function start(code) {
      // Always populated by defaults.
      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(
        self.parser.constructs.disable.null,
        'expected `disable.null` to be populated'
      )

      // To do: `enter` here or in next state?
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)
      return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)
        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(
            effects,
            beforeSequenceClose,
            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,
            self.parser.constructs.disable.null.includes('codeIndented')
              ? undefined
              : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.tabSize
          )(code)
        : beforeSequenceClose(code)
    }

    /**
     * In closing fence, after optional whitespace, at sequence.
     *
     * ```markdown
     *   | ~~~js
     *   | alert(1)
     * > | ~~~
     *     ^
     * ```
     *
     * @type {State}
     */
    function beforeSequenceClose(code) {
      if (code === marker) {
        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)
        return sequenceClose(code)
      }

      return nok(code)
    }

    /**
     * In closing fence sequence.
     *
     * ```markdown
     *   | ~~~js
     *   | alert(1)
     * > | ~~~
     *     ^
     * ```
     *
     * @type {State}
     */
    function sequenceClose(code) {
      if (code === marker) {
        size++
        effects.consume(code)
        return sequenceClose
      }

      if (size >= sizeOpen) {
        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)
        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)
          ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, sequenceCloseAfter, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)
          : sequenceCloseAfter(code)
      }

      return nok(code)
    }

    /**
     * After closing fence sequence, after optional whitespace.
     *
     * ```markdown
     *   | ~~~js
     *   | alert(1)
     * > | ~~~
     *        ^
     * ```
     *
     * @type {State}
     */
    function sequenceCloseAfter(code) {
      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)
        return ok(code)
      }

      return nok(code)
    }
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeNonLazyContinuation(effects, ok, nok) {
  const self = this

  return start

  /**
   *
   *
   * @type {State}
   */
  function start(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {
      return nok(code)
    }

    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    return lineStart
  }

  /**
   *
   *
   * @type {State}
   */
  function lineStart(code) {
    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/code-indented.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   codeIndented: () => (/* binding */ codeIndented)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const codeIndented = {
  name: 'codeIndented',
  tokenize: tokenizeCodeIndented
}

/** @type {Construct} */
const furtherStart = {partial: true, tokenize: tokenizeFurtherStart}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeCodeIndented(effects, ok, nok) {
  const self = this
  return start

  /**
   * Start of code (indented).
   *
   * > **Parsing note**: it is not needed to check if this first line is a
   * > filled line (that it has a non-whitespace character), because blank lines
   * > are parsed already, so we never run into that.
   *
   * ```markdown
   * > |     aaa
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    // To do: manually check if interrupting like `markdown-rs`.
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownSpace)(code))
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeIndented)
    // To do: use an improved `space_or_tab` function like `markdown-rs`,
    // so that we can drop the next state.
    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(
      effects,
      afterPrefix,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1
    )(code)
  }

  /**
   * At start, after 1 or 4 spaces.
   *
   * ```markdown
   * > |     aaa
   *         ^
   * ```
   *
   * @type {State}
   */
  function afterPrefix(code) {
    const tail = self.events[self.events.length - 1]
    return tail &&
      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix &&
      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize
      ? atBreak(code)
      : nok(code)
  }

  /**
   * At a break.
   *
   * ```markdown
   * > |     aaa
   *         ^  ^
   * ```
   *
   * @type {State}
   */
  function atBreak(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {
      return after(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {
      return effects.attempt(furtherStart, atBreak, after)(code)
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)
    return inside(code)
  }

  /**
   * In code content.
   *
   * ```markdown
   * > |     aaa
   *         ^^^^
   * ```
   *
   * @type {State}
   */
  function inside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)
      return atBreak(code)
    }

    effects.consume(code)
    return inside
  }

  /** @type {State} */
  function after(code) {
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeIndented)
    // To do: allow interrupting like `markdown-rs`.
    // Feel free to interrupt.
    // tokenizer.interrupt = false
    return ok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeFurtherStart(effects, ok, nok) {
  const self = this

  return furtherStart

  /**
   * At eol, trying to parse another indent.
   *
   * ```markdown
   * > |     aaa
   *            ^
   *   |     bbb
   * ```
   *
   * @type {State}
   */
  function furtherStart(code) {
    // To do: improve `lazy` / `pierce` handling.
    // If this is a lazy line, it can’t be code.
    if (self.parser.lazy[self.now().line]) {
      return nok(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
      return furtherStart
    }

    // To do: the code here in `micromark-js` is a bit different from
    // `markdown-rs` because there it can attempt spaces.
    // We can’t yet.
    //
    // To do: use an improved `space_or_tab` function like `markdown-rs`,
    // so that we can drop the next state.
    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(
      effects,
      afterPrefix,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1
    )(code)
  }

  /**
   * At start, after 1 or 4 spaces.
   *
   * ```markdown
   * > |     aaa
   *         ^
   * ```
   *
   * @type {State}
   */
  function afterPrefix(code) {
    const tail = self.events[self.events.length - 1]
    return tail &&
      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix &&
      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize
      ? ok(code)
      : (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)
        ? furtherStart(code)
        : nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/code-text.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   codeText: () => (/* binding */ codeText)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/**
 * @import {
 *   Construct,
 *   Previous,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer,
 *   Token
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const codeText = {
  name: 'codeText',
  previous,
  resolve: resolveCodeText,
  tokenize: tokenizeCodeText
}

// To do: next major: don’t resolve, like `markdown-rs`.
/** @type {Resolver} */
function resolveCodeText(events) {
  let tailExitIndex = events.length - 4
  let headEnterIndex = 3
  /** @type {number} */
  let index
  /** @type {number | undefined} */
  let enter

  // If we start and end with an EOL or a space.
  if (
    (events[headEnterIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||
      events[headEnterIndex][1].type === 'space') &&
    (events[tailExitIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||
      events[tailExitIndex][1].type === 'space')
  ) {
    index = headEnterIndex

    // And we have data.
    while (++index < tailExitIndex) {
      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData) {
        // Then we have padding.
        events[headEnterIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextPadding
        events[tailExitIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextPadding
        headEnterIndex += 2
        tailExitIndex -= 2
        break
      }
    }
  }

  // Merge adjacent spaces and data.
  index = headEnterIndex - 1
  tailExitIndex++

  while (++index <= tailExitIndex) {
    if (enter === undefined) {
      if (
        index !== tailExitIndex &&
        events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding
      ) {
        enter = index
      }
    } else if (
      index === tailExitIndex ||
      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding
    ) {
      events[enter][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData

      if (index !== enter + 2) {
        events[enter][1].end = events[index - 1][1].end
        events.splice(enter + 2, index - enter - 2)
        tailExitIndex -= index - enter - 2
        index = enter + 2
      }

      enter = undefined
    }
  }

  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Previous}
 */
function previous(code) {
  // If there is a previous code, there will always be a tail.
  return (
    code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent ||
    this.events[this.events.length - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.characterEscape
  )
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeCodeText(effects, ok, nok) {
  const self = this
  let sizeOpen = 0
  /** @type {number} */
  let size
  /** @type {Token} */
  let token

  return start

  /**
   * Start of code (text).
   *
   * ```markdown
   * > | `a`
   *     ^
   * > | \`a`
   *      ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent, 'expected `` ` ``')
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(previous.call(self, self.previous), 'expected correct previous')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeText)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)
    return sequenceOpen(code)
  }

  /**
   * In opening sequence.
   *
   * ```markdown
   * > | `a`
   *     ^
   * ```
   *
   * @type {State}
   */
  function sequenceOpen(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent) {
      effects.consume(code)
      sizeOpen++
      return sequenceOpen
    }

    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)
    return between(code)
  }

  /**
   * Between something and something else.
   *
   * ```markdown
   * > | `a`
   *      ^^
   * ```
   *
   * @type {State}
   */
  function between(code) {
    // EOF.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {
      return nok(code)
    }

    // To do: next major: don’t do spaces in resolve, but when compiling,
    // like `markdown-rs`.
    // Tabs don’t work, and virtual spaces don’t make sense.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space) {
      effects.enter('space')
      effects.consume(code)
      effects.exit('space')
      return between
    }

    // Closing fence? Could also be data.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent) {
      token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)
      size = 0
      return sequenceClose(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
      return between
    }

    // Data.
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData)
    return data(code)
  }

  /**
   * In data.
   *
   * ```markdown
   * > | `a`
   *      ^
   * ```
   *
   * @type {State}
   */
  function data(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)
    ) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData)
      return between(code)
    }

    effects.consume(code)
    return data
  }

  /**
   * In closing sequence.
   *
   * ```markdown
   * > | `a`
   *       ^
   * ```
   *
   * @type {State}
   */
  function sequenceClose(code) {
    // More.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent) {
      effects.consume(code)
      size++
      return sequenceClose
    }

    // Done!
    if (size === sizeOpen) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeText)
      return ok(code)
    }

    // More or less accents: mark as data.
    token.type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData
    return data(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js":
/*!*******************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/content.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   content: () => (/* binding */ content)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ "(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Construct,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer,
 *   Token
 * } from 'micromark-util-types'
 */







/**
 * No name because it must not be turned off.
 * @type {Construct}
 */
const content = {resolve: resolveContent, tokenize: tokenizeContent}

/** @type {Construct} */
const continuationConstruct = {partial: true, tokenize: tokenizeContinuation}

/**
 * Content is transparent: it’s parsed right now. That way, definitions are also
 * parsed right now: before text in paragraphs (specifically, media) are parsed.
 *
 * @type {Resolver}
 */
function resolveContent(events) {
  ;(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)
  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeContent(effects, ok) {
  /** @type {Token | undefined} */
  let previous

  return chunkStart

  /**
   * Before a content chunk.
   *
   * ```markdown
   * > | abc
   *     ^
   * ```
   *
   * @type {State}
   */
  function chunkStart(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
      code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code),
      'expected no eof or eol'
    )

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.content)
    previous = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent, {
      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeContent
    })
    return chunkInside(code)
  }

  /**
   * In a content chunk.
   *
   * ```markdown
   * > | abc
   *     ^^^
   * ```
   *
   * @type {State}
   */
  function chunkInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {
      return contentEnd(code)
    }

    // To do: in `markdown-rs`, each line is parsed on its own, and everything
    // is stitched together resolving.
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      return effects.check(
        continuationConstruct,
        contentContinue,
        contentEnd
      )(code)
    }

    // Data.
    effects.consume(code)
    return chunkInside
  }

  /**
   *
   *
   * @type {State}
   */
  function contentEnd(code) {
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.content)
    return ok(code)
  }

  /**
   *
   *
   * @type {State}
   */
  function contentContinue(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected eol')
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(previous, 'expected previous token')
    previous.next = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent, {
      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeContent,
      previous
    })
    previous = previous.next
    return chunkInside
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeContinuation(effects, ok, nok) {
  const self = this

  return startLookahead

  /**
   *
   *
   * @type {State}
   */
  function startLookahead(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected a line ending')
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)
    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, prefixed, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix)
  }

  /**
   *
   *
   * @type {State}
   */
  function prefixed(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      return nok(code)
    }

    // Always populated by defaults.
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
      self.parser.constructs.disable.null,
      'expected `disable.null` to be populated'
    )

    const tail = self.events[self.events.length - 1]

    if (
      !self.parser.constructs.disable.null.includes('codeIndented') &&
      tail &&
      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix &&
      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize
    ) {
      return ok(code)
    }

    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   definition: () => (/* binding */ definition)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_destination__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-destination */ "(ssr)/./node_modules/micromark-factory-destination/dev/index.js");
/* harmony import */ var micromark_factory_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-label */ "(ssr)/./node_modules/micromark-factory-label/dev/index.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_factory_title__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-factory-title */ "(ssr)/./node_modules/micromark-factory-title/dev/index.js");
/* harmony import */ var micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-whitespace */ "(ssr)/./node_modules/micromark-factory-whitespace/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-normalize-identifier */ "(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */











/** @type {Construct} */
const definition = {name: 'definition', tokenize: tokenizeDefinition}

/** @type {Construct} */
const titleBefore = {partial: true, tokenize: tokenizeTitleBefore}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeDefinition(effects, ok, nok) {
  const self = this
  /** @type {string} */
  let identifier

  return start

  /**
   * At start of a definition.
   *
   * ```markdown
   * > | [a]: b "c"
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    // Do not interrupt paragraphs (but do follow definitions).
    // To do: do `interrupt` the way `markdown-rs` does.
    // To do: parse whitespace the way `markdown-rs` does.
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definition)
    return before(code)
  }

  /**
   * After optional whitespace, at `[`.
   *
   * ```markdown
   * > | [a]: b "c"
   *     ^
   * ```
   *
   * @type {State}
   */
  function before(code) {
    // To do: parse whitespace the way `markdown-rs` does.
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket, 'expected `[`')
    return micromark_factory_label__WEBPACK_IMPORTED_MODULE_3__.factoryLabel.call(
      self,
      effects,
      labelAfter,
      // Note: we don’t need to reset the way `markdown-rs` does.
      nok,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionLabel,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionLabelMarker,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionLabelString
    )(code)
  }

  /**
   * After label.
   *
   * ```markdown
   * > | [a]: b "c"
   *        ^
   * ```
   *
   * @type {State}
   */
  function labelAfter(code) {
    identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_4__.normalizeIdentifier)(
      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)
    )

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionMarker)
      return markerAfter
    }

    return nok(code)
  }

  /**
   * After marker.
   *
   * ```markdown
   * > | [a]: b "c"
   *         ^
   * ```
   *
   * @type {State}
   */
  function markerAfter(code) {
    // Note: whitespace is optional.
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)
      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__.factoryWhitespace)(effects, destinationBefore)(code)
      : destinationBefore(code)
  }

  /**
   * Before destination.
   *
   * ```markdown
   * > | [a]: b "c"
   *          ^
   * ```
   *
   * @type {State}
   */
  function destinationBefore(code) {
    return (0,micromark_factory_destination__WEBPACK_IMPORTED_MODULE_7__.factoryDestination)(
      effects,
      destinationAfter,
      // Note: we don’t need to reset the way `markdown-rs` does.
      nok,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestination,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationLiteral,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationLiteralMarker,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationRaw,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationString
    )(code)
  }

  /**
   * After destination.
   *
   * ```markdown
   * > | [a]: b "c"
   *           ^
   * ```
   *
   * @type {State}
   */
  function destinationAfter(code) {
    return effects.attempt(titleBefore, after, after)(code)
  }

  /**
   * After definition.
   *
   * ```markdown
   * > | [a]: b
   *           ^
   * > | [a]: b "c"
   *               ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__.factorySpace)(effects, afterWhitespace, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)
      : afterWhitespace(code)
  }

  /**
   * After definition, after optional whitespace.
   *
   * ```markdown
   * > | [a]: b
   *           ^
   * > | [a]: b "c"
   *               ^
   * ```
   *
   * @type {State}
   */
  function afterWhitespace(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definition)

      // Note: we don’t care about uniqueness.
      // It’s likely that that doesn’t happen very frequently.
      // It is more likely that it wastes precious time.
      self.parser.defined.push(identifier)

      // To do: `markdown-rs` interrupt.
      // // You’d be interrupting.
      // tokenizer.interrupt = true
      return ok(code)
    }

    return nok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeTitleBefore(effects, ok, nok) {
  return titleBefore

  /**
   * After destination, at whitespace.
   *
   * ```markdown
   * > | [a]: b
   *           ^
   * > | [a]: b "c"
   *           ^
   * ```
   *
   * @type {State}
   */
  function titleBefore(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)
      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__.factoryWhitespace)(effects, beforeMarker)(code)
      : nok(code)
  }

  /**
   * At title.
   *
   * ```markdown
   *   | [a]: b
   * > | "c"
   *     ^
   * ```
   *
   * @type {State}
   */
  function beforeMarker(code) {
    return (0,micromark_factory_title__WEBPACK_IMPORTED_MODULE_9__.factoryTitle)(
      effects,
      titleAfter,
      nok,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionTitle,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionTitleMarker,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionTitleString
    )(code)
  }

  /**
   * After title.
   *
   * ```markdown
   * > | [a]: b "c"
   *               ^
   * ```
   *
   * @type {State}
   */
  function titleAfter(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__.factorySpace)(
          effects,
          titleAfterOptionalWhitespace,
          micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace
        )(code)
      : titleAfterOptionalWhitespace(code)
  }

  /**
   * After title, after optional whitespace.
   *
   * ```markdown
   * > | [a]: b "c"
   *               ^
   * ```
   *
   * @type {State}
   */
  function titleAfterOptionalWhitespace(code) {
    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code) ? ok(code) : nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   hardBreakEscape: () => (/* binding */ hardBreakEscape)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const hardBreakEscape = {
  name: 'hardBreakEscape',
  tokenize: tokenizeHardBreakEscape
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeHardBreakEscape(effects, ok, nok) {
  return start

  /**
   * Start of a hard break (escape).
   *
   * ```markdown
   * > | a\
   *      ^
   *   | b
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash, 'expected `\\`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.hardBreakEscape)
    effects.consume(code)
    return after
  }

  /**
   * After `\`, at eol.
   *
   * ```markdown
   * > | a\
   *       ^
   *   | b
   * ```
   *
   *  @type {State}
   */
  function after(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.hardBreakEscape)
      return ok(code)
    }

    return nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   headingAtx: () => (/* binding */ headingAtx)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ "(ssr)/./node_modules/micromark-util-chunked/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/**
 * @import {
 *   Construct,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer,
 *   Token
 * } from 'micromark-util-types'
 */







/** @type {Construct} */
const headingAtx = {
  name: 'headingAtx',
  resolve: resolveHeadingAtx,
  tokenize: tokenizeHeadingAtx
}

/** @type {Resolver} */
function resolveHeadingAtx(events, context) {
  let contentEnd = events.length - 2
  let contentStart = 3
  /** @type {Token} */
  let content
  /** @type {Token} */
  let text

  // Prefix whitespace, part of the opening.
  if (events[contentStart][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace) {
    contentStart += 2
  }

  // Suffix whitespace, part of the closing.
  if (
    contentEnd - 2 > contentStart &&
    events[contentEnd][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace
  ) {
    contentEnd -= 2
  }

  if (
    events[contentEnd][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence &&
    (contentStart === contentEnd - 1 ||
      (contentEnd - 4 > contentStart &&
        events[contentEnd - 2][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace))
  ) {
    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4
  }

  if (contentEnd > contentStart) {
    content = {
      type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingText,
      start: events[contentStart][1].start,
      end: events[contentEnd][1].end
    }
    text = {
      type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText,
      start: events[contentStart][1].start,
      end: events[contentEnd][1].end,
      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.contentTypeText
    }

    ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(events, contentStart, contentEnd - contentStart + 1, [
      ['enter', content, context],
      ['enter', text, context],
      ['exit', text, context],
      ['exit', content, context]
    ])
  }

  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeHeadingAtx(effects, ok, nok) {
  let size = 0

  return start

  /**
   * Start of a heading (atx).
   *
   * ```markdown
   * > | ## aa
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    // To do: parse indent like `markdown-rs`.
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeading)
    return before(code)
  }

  /**
   * After optional whitespace, at `#`.
   *
   * ```markdown
   * > | ## aa
   *     ^
   * ```
   *
   * @type {State}
   */
  function before(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign, 'expected `#`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)
    return sequenceOpen(code)
  }

  /**
   * In opening sequence.
   *
   * ```markdown
   * > | ## aa
   *     ^
   * ```
   *
   * @type {State}
   */
  function sequenceOpen(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign &&
      size++ < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.atxHeadingOpeningFenceSizeMax
    ) {
      effects.consume(code)
      return sequenceOpen
    }

    // Always at least one `#`.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)
      return atBreak(code)
    }

    return nok(code)
  }

  /**
   * After something, before something else.
   *
   * ```markdown
   * > | ## aa
   *       ^
   * ```
   *
   * @type {State}
   */
  function atBreak(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)
      return sequenceFurther(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeading)
      // To do: interrupt like `markdown-rs`.
      // // Feel free to interrupt.
      // tokenizer.interrupt = false
      return ok(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)) {
      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, atBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)
    }

    // To do: generate `data` tokens, add the `text` token later.
    // Needs edit map, see: `markdown.rs`.
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingText)
    return data(code)
  }

  /**
   * In further sequence (after whitespace).
   *
   * Could be normal “visible” hashes in the heading or a final sequence.
   *
   * ```markdown
   * > | ## aa ##
   *           ^
   * ```
   *
   * @type {State}
   */
  function sequenceFurther(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign) {
      effects.consume(code)
      return sequenceFurther
    }

    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)
    return atBreak(code)
  }

  /**
   * In text.
   *
   * ```markdown
   * > | ## aa
   *        ^
   * ```
   *
   * @type {State}
   */
  function data(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)
    ) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingText)
      return atBreak(code)
    }

    effects.consume(code)
    return data
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/html-flow.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   htmlFlow: () => (/* binding */ htmlFlow)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-html-tag-name */ "(ssr)/./node_modules/micromark-util-html-tag-name/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/* harmony import */ var _blank_line_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blank-line.js */ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */







/** @type {Construct} */
const htmlFlow = {
  concrete: true,
  name: 'htmlFlow',
  resolveTo: resolveToHtmlFlow,
  tokenize: tokenizeHtmlFlow
}

/** @type {Construct} */
const blankLineBefore = {partial: true, tokenize: tokenizeBlankLineBefore}
const nonLazyContinuationStart = {
  partial: true,
  tokenize: tokenizeNonLazyContinuationStart
}

/** @type {Resolver} */
function resolveToHtmlFlow(events) {
  let index = events.length

  while (index--) {
    if (
      events[index][0] === 'enter' &&
      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlow
    ) {
      break
    }
  }

  if (index > 1 && events[index - 2][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix) {
    // Add the prefix start to the HTML token.
    events[index][1].start = events[index - 2][1].start
    // Add the prefix start to the HTML line token.
    events[index + 1][1].start = events[index - 2][1].start
    // Remove the line prefix.
    events.splice(index - 2, 2)
  }

  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeHtmlFlow(effects, ok, nok) {
  const self = this
  /** @type {number} */
  let marker
  /** @type {boolean} */
  let closingTag
  /** @type {string} */
  let buffer
  /** @type {number} */
  let index
  /** @type {Code} */
  let markerB

  return start

  /**
   * Start of HTML (flow).
   *
   * ```markdown
   * > | <x />
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    // To do: parse indent like `markdown-rs`.
    return before(code)
  }

  /**
   * At `<`, after optional whitespace.
   *
   * ```markdown
   * > | <x />
   *     ^
   * ```
   *
   * @type {State}
   */
  function before(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan, 'expected `<`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlow)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)
    effects.consume(code)
    return open
  }

  /**
   * After `<`, at tag name or other stuff.
   *
   * ```markdown
   * > | <x />
   *      ^
   * > | <!doctype>
   *      ^
   * > | <!--xxx-->
   *      ^
   * ```
   *
   * @type {State}
   */
  function open(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.exclamationMark) {
      effects.consume(code)
      return declarationOpen
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {
      effects.consume(code)
      closingTag = true
      return tagCloseStart
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.questionMark) {
      effects.consume(code)
      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlInstruction
      // To do:
      // tokenizer.concrete = true
      // To do: use `markdown-rs` style interrupt.
      // While we’re in an instruction instead of a declaration, we’re on a `?`
      // right now, so we do need to search for `>`, similar to declarations.
      return self.interrupt ? ok : continuationDeclarationInside
    }

    // ASCII alphabetical.
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {
      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== null) // Always the case.
      effects.consume(code)
      buffer = String.fromCharCode(code)
      return tagName
    }

    return nok(code)
  }

  /**
   * After `<!`, at declaration, comment, or CDATA.
   *
   * ```markdown
   * > | <!doctype>
   *       ^
   * > | <!--xxx-->
   *       ^
   * > | <![CDATA[>&<]]>
   *       ^
   * ```
   *
   * @type {State}
   */
  function declarationOpen(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {
      effects.consume(code)
      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment
      return commentOpenInside
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket) {
      effects.consume(code)
      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlCdata
      index = 0
      return cdataOpenInside
    }

    // ASCII alphabetical.
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {
      effects.consume(code)
      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlDeclaration
      // // Do not form containers.
      // tokenizer.concrete = true
      return self.interrupt ? ok : continuationDeclarationInside
    }

    return nok(code)
  }

  /**
   * After `<!-`, inside a comment, at another `-`.
   *
   * ```markdown
   * > | <!--xxx-->
   *        ^
   * ```
   *
   * @type {State}
   */
  function commentOpenInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {
      effects.consume(code)
      // // Do not form containers.
      // tokenizer.concrete = true
      return self.interrupt ? ok : continuationDeclarationInside
    }

    return nok(code)
  }

  /**
   * After `<![`, inside CDATA, expecting `CDATA[`.
   *
   * ```markdown
   * > | <![CDATA[>&<]]>
   *        ^^^^^^
   * ```
   *
   * @type {State}
   */
  function cdataOpenInside(code) {
    const value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.cdataOpeningString

    if (code === value.charCodeAt(index++)) {
      effects.consume(code)

      if (index === value.length) {
        // // Do not form containers.
        // tokenizer.concrete = true
        return self.interrupt ? ok : continuation
      }

      return cdataOpenInside
    }

    return nok(code)
  }

  /**
   * After `</`, in closing tag, at tag name.
   *
   * ```markdown
   * > | </x>
   *       ^
   * ```
   *
   * @type {State}
   */
  function tagCloseStart(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {
      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== null) // Always the case.
      effects.consume(code)
      buffer = String.fromCharCode(code)
      return tagName
    }

    return nok(code)
  }

  /**
   * In tag name.
   *
   * ```markdown
   * > | <ab>
   *      ^^
   * > | </ab>
   *       ^^
   * ```
   *
   * @type {State}
   */
  function tagName(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEndingOrSpace)(code)
    ) {
      const slash = code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash
      const name = buffer.toLowerCase()

      if (!slash && !closingTag && micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlRawNames.includes(name)) {
        marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRaw
        // // Do not form containers.
        // tokenizer.concrete = true
        return self.interrupt ? ok(code) : continuation(code)
      }

      if (micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlBlockNames.includes(buffer.toLowerCase())) {
        marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlBasic

        if (slash) {
          effects.consume(code)
          return basicSelfClosing
        }

        // // Do not form containers.
        // tokenizer.concrete = true
        return self.interrupt ? ok(code) : continuation(code)
      }

      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComplete
      // Do not support complete HTML when interrupting.
      return self.interrupt && !self.parser.lazy[self.now().line]
        ? nok(code)
        : closingTag
          ? completeClosingTagAfter(code)
          : completeAttributeNameBefore(code)
    }

    // ASCII alphanumerical and `-`.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric)(code)) {
      effects.consume(code)
      buffer += String.fromCharCode(code)
      return tagName
    }

    return nok(code)
  }

  /**
   * After closing slash of a basic tag name.
   *
   * ```markdown
   * > | <div/>
   *          ^
   * ```
   *
   * @type {State}
   */
  function basicSelfClosing(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {
      effects.consume(code)
      // // Do not form containers.
      // tokenizer.concrete = true
      return self.interrupt ? ok : continuation
    }

    return nok(code)
  }

  /**
   * After closing slash of a complete tag name.
   *
   * ```markdown
   * > | <x/>
   *        ^
   * ```
   *
   * @type {State}
   */
  function completeClosingTagAfter(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {
      effects.consume(code)
      return completeClosingTagAfter
    }

    return completeEnd(code)
  }

  /**
   * At an attribute name.
   *
   * At first, this state is used after a complete tag name, after whitespace,
   * where it expects optional attributes or the end of the tag.
   * It is also reused after attributes, when expecting more optional
   * attributes.
   *
   * ```markdown
   * > | <a />
   *        ^
   * > | <a :b>
   *        ^
   * > | <a _b>
   *        ^
   * > | <a b>
   *        ^
   * > | <a >
   *        ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeNameBefore(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {
      effects.consume(code)
      return completeEnd
    }

    // ASCII alphanumerical and `:` and `_`.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {
      effects.consume(code)
      return completeAttributeName
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {
      effects.consume(code)
      return completeAttributeNameBefore
    }

    return completeEnd(code)
  }

  /**
   * In attribute name.
   *
   * ```markdown
   * > | <a :b>
   *         ^
   * > | <a _b>
   *         ^
   * > | <a b>
   *         ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeName(code) {
    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dot ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.colon ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.underscore ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric)(code)
    ) {
      effects.consume(code)
      return completeAttributeName
    }

    return completeAttributeNameAfter(code)
  }

  /**
   * After attribute name, at an optional initializer, the end of the tag, or
   * whitespace.
   *
   * ```markdown
   * > | <a b>
   *         ^
   * > | <a b=c>
   *         ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeNameAfter(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo) {
      effects.consume(code)
      return completeAttributeValueBefore
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {
      effects.consume(code)
      return completeAttributeNameAfter
    }

    return completeAttributeNameBefore(code)
  }

  /**
   * Before unquoted, double quoted, or single quoted attribute value, allowing
   * whitespace.
   *
   * ```markdown
   * > | <a b=c>
   *          ^
   * > | <a b="c">
   *          ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeValueBefore(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent
    ) {
      return nok(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.apostrophe) {
      effects.consume(code)
      markerB = code
      return completeAttributeValueQuoted
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {
      effects.consume(code)
      return completeAttributeValueBefore
    }

    return completeAttributeValueUnquoted(code)
  }

  /**
   * In double or single quoted attribute value.
   *
   * ```markdown
   * > | <a b="c">
   *           ^
   * > | <a b='c'>
   *           ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeValueQuoted(code) {
    if (code === markerB) {
      effects.consume(code)
      markerB = null
      return completeAttributeValueQuotedAfter
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      return nok(code)
    }

    effects.consume(code)
    return completeAttributeValueQuoted
  }

  /**
   * In unquoted attribute value.
   *
   * ```markdown
   * > | <a b=c>
   *          ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeValueUnquoted(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.quotationMark ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.apostrophe ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEndingOrSpace)(code)
    ) {
      return completeAttributeNameAfter(code)
    }

    effects.consume(code)
    return completeAttributeValueUnquoted
  }

  /**
   * After double or single quoted attribute value, before whitespace or the
   * end of the tag.
   *
   * ```markdown
   * > | <a b="c">
   *            ^
   * ```
   *
   * @type {State}
   */
  function completeAttributeValueQuotedAfter(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)
    ) {
      return completeAttributeNameBefore(code)
    }

    return nok(code)
  }

  /**
   * In certain circumstances of a complete tag where only an `>` is allowed.
   *
   * ```markdown
   * > | <a b="c">
   *             ^
   * ```
   *
   * @type {State}
   */
  function completeEnd(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {
      effects.consume(code)
      return completeAfter
    }

    return nok(code)
  }

  /**
   * After `>` in a complete tag.
   *
   * ```markdown
   * > | <x>
   *        ^
   * ```
   *
   * @type {State}
   */
  function completeAfter(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      // // Do not form containers.
      // tokenizer.concrete = true
      return continuation(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {
      effects.consume(code)
      return completeAfter
    }

    return nok(code)
  }

  /**
   * In continuation of any HTML kind.
   *
   * ```markdown
   * > | <!--xxx-->
   *          ^
   * ```
   *
   * @type {State}
   */
  function continuation(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment) {
      effects.consume(code)
      return continuationCommentInside
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRaw) {
      effects.consume(code)
      return continuationRawTagOpen
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlDeclaration) {
      effects.consume(code)
      return continuationClose
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.questionMark && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlInstruction) {
      effects.consume(code)
      return continuationDeclarationInside
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.rightSquareBracket && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlCdata) {
      effects.consume(code)
      return continuationCdataInside
    }

    if (
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code) &&
      (marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlBasic || marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComplete)
    ) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)
      return effects.check(
        blankLineBefore,
        continuationAfter,
        continuationStart
      )(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)
      return continuationStart(code)
    }

    effects.consume(code)
    return continuation
  }

  /**
   * In continuation, at eol.
   *
   * ```markdown
   * > | <x>
   *        ^
   *   | asd
   * ```
   *
   * @type {State}
   */
  function continuationStart(code) {
    return effects.check(
      nonLazyContinuationStart,
      continuationStartNonLazy,
      continuationAfter
    )(code)
  }

  /**
   * In continuation, at eol, before non-lazy content.
   *
   * ```markdown
   * > | <x>
   *        ^
   *   | asd
   * ```
   *
   * @type {State}
   */
  function continuationStartNonLazy(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code))
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
    return continuationBefore
  }

  /**
   * In continuation, before non-lazy content.
   *
   * ```markdown
   *   | <x>
   * > | asd
   *     ^
   * ```
   *
   * @type {State}
   */
  function continuationBefore(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      return continuationStart(code)
    }

    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)
    return continuation(code)
  }

  /**
   * In comment continuation, after one `-`, expecting another.
   *
   * ```markdown
   * > | <!--xxx-->
   *             ^
   * ```
   *
   * @type {State}
   */
  function continuationCommentInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {
      effects.consume(code)
      return continuationDeclarationInside
    }

    return continuation(code)
  }

  /**
   * In raw continuation, after `<`, at `/`.
   *
   * ```markdown
   * > | <script>console.log(1)</script>
   *                            ^
   * ```
   *
   * @type {State}
   */
  function continuationRawTagOpen(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {
      effects.consume(code)
      buffer = ''
      return continuationRawEndTag
    }

    return continuation(code)
  }

  /**
   * In raw continuation, after `</`, in a raw tag name.
   *
   * ```markdown
   * > | <script>console.log(1)</script>
   *                             ^^^^^^
   * ```
   *
   * @type {State}
   */
  function continuationRawEndTag(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {
      const name = buffer.toLowerCase()

      if (micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlRawNames.includes(name)) {
        effects.consume(code)
        return continuationClose
      }

      return continuation(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code) && buffer.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRawSizeMax) {
      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== null) // Always the case.
      effects.consume(code)
      buffer += String.fromCharCode(code)
      return continuationRawEndTag
    }

    return continuation(code)
  }

  /**
   * In cdata continuation, after `]`, expecting `]>`.
   *
   * ```markdown
   * > | <![CDATA[>&<]]>
   *                  ^
   * ```
   *
   * @type {State}
   */
  function continuationCdataInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.rightSquareBracket) {
      effects.consume(code)
      return continuationDeclarationInside
    }

    return continuation(code)
  }

  /**
   * In declaration or instruction continuation, at `>`.
   *
   * ```markdown
   * > | <!-->
   *         ^
   * > | <?>
   *       ^
   * > | <!q>
   *        ^
   * > | <!--ab-->
   *             ^
   * > | <![CDATA[>&<]]>
   *                   ^
   * ```
   *
   * @type {State}
   */
  function continuationDeclarationInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {
      effects.consume(code)
      return continuationClose
    }

    // More dashes.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment) {
      effects.consume(code)
      return continuationDeclarationInside
    }

    return continuation(code)
  }

  /**
   * In closed continuation: everything we get until the eol/eof is part of it.
   *
   * ```markdown
   * > | <!doctype>
   *               ^
   * ```
   *
   * @type {State}
   */
  function continuationClose(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)
      return continuationAfter(code)
    }

    effects.consume(code)
    return continuationClose
  }

  /**
   * Done.
   *
   * ```markdown
   * > | <!doctype>
   *               ^
   * ```
   *
   * @type {State}
   */
  function continuationAfter(code) {
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlow)
    // // Feel free to interrupt.
    // tokenizer.interrupt = false
    // // No longer concrete.
    // tokenizer.concrete = false
    return ok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeNonLazyContinuationStart(effects, ok, nok) {
  const self = this

  return start

  /**
   * At eol, before continuation.
   *
   * ```markdown
   * > | * ```js
   *            ^
   *   | b
   * ```
   *
   * @type {State}
   */
  function start(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
      return after
    }

    return nok(code)
  }

  /**
   * A continuation.
   *
   * ```markdown
   *   | * ```js
   * > | b
   *     ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeBlankLineBefore(effects, ok, nok) {
  return start

  /**
   * Before eol, expecting blank line.
   *
   * ```markdown
   * > | <div>
   *          ^
   *   |
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected a line ending')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)
    return effects.attempt(_blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine, ok, nok)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/html-text.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   htmlText: () => (/* binding */ htmlText)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeHtmlText(effects, ok, nok) {
  const self = this
  /** @type {NonNullable<Code> | undefined} */
  let marker
  /** @type {number} */
  let index
  /** @type {State} */
  let returnState

  return start

  /**
   * Start of HTML (text).
   *
   * ```markdown
   * > | a <b> c
   *       ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlText)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)
    effects.consume(code)
    return open
  }

  /**
   * After `<`, at tag name or other stuff.
   *
   * ```markdown
   * > | a <b> c
   *        ^
   * > | a <!doctype> c
   *        ^
   * > | a <!--b--> c
   *        ^
   * ```
   *
   * @type {State}
   */
  function open(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.exclamationMark) {
      effects.consume(code)
      return declarationOpen
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {
      effects.consume(code)
      return tagCloseStart
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.questionMark) {
      effects.consume(code)
      return instruction
    }

    // ASCII alphabetical.
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {
      effects.consume(code)
      return tagOpen
    }

    return nok(code)
  }

  /**
   * After `<!`, at declaration, comment, or CDATA.
   *
   * ```markdown
   * > | a <!doctype> c
   *         ^
   * > | a <!--b--> c
   *         ^
   * > | a <![CDATA[>&<]]> c
   *         ^
   * ```
   *
   * @type {State}
   */
  function declarationOpen(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {
      effects.consume(code)
      return commentOpenInside
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftSquareBracket) {
      effects.consume(code)
      index = 0
      return cdataOpenInside
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {
      effects.consume(code)
      return declaration
    }

    return nok(code)
  }

  /**
   * In a comment, after `<!-`, at another `-`.
   *
   * ```markdown
   * > | a <!--b--> c
   *          ^
   * ```
   *
   * @type {State}
   */
  function commentOpenInside(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {
      effects.consume(code)
      return commentEnd
    }

    return nok(code)
  }

  /**
   * In comment.
   *
   * ```markdown
   * > | a <!--b--> c
   *           ^
   * ```
   *
   * @type {State}
   */
  function comment(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {
      return nok(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {
      effects.consume(code)
      return commentClose
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = comment
      return lineEndingBefore(code)
    }

    effects.consume(code)
    return comment
  }

  /**
   * In comment, after `-`.
   *
   * ```markdown
   * > | a <!--b--> c
   *             ^
   * ```
   *
   * @type {State}
   */
  function commentClose(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {
      effects.consume(code)
      return commentEnd
    }

    return comment(code)
  }

  /**
   * In comment, after `--`.
   *
   * ```markdown
   * > | a <!--b--> c
   *              ^
   * ```
   *
   * @type {State}
   */
  function commentEnd(code) {
    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan
      ? end(code)
      : code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash
        ? commentClose(code)
        : comment(code)
  }

  /**
   * After `<![`, in CDATA, expecting `CDATA[`.
   *
   * ```markdown
   * > | a <![CDATA[>&<]]> b
   *          ^^^^^^
   * ```
   *
   * @type {State}
   */
  function cdataOpenInside(code) {
    const value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.cdataOpeningString

    if (code === value.charCodeAt(index++)) {
      effects.consume(code)
      return index === value.length ? cdata : cdataOpenInside
    }

    return nok(code)
  }

  /**
   * In CDATA.
   *
   * ```markdown
   * > | a <![CDATA[>&<]]> b
   *                ^^^
   * ```
   *
   * @type {State}
   */
  function cdata(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {
      return nok(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {
      effects.consume(code)
      return cdataClose
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = cdata
      return lineEndingBefore(code)
    }

    effects.consume(code)
    return cdata
  }

  /**
   * In CDATA, after `]`, at another `]`.
   *
   * ```markdown
   * > | a <![CDATA[>&<]]> b
   *                    ^
   * ```
   *
   * @type {State}
   */
  function cdataClose(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {
      effects.consume(code)
      return cdataEnd
    }

    return cdata(code)
  }

  /**
   * In CDATA, after `]]`, at `>`.
   *
   * ```markdown
   * > | a <![CDATA[>&<]]> b
   *                     ^
   * ```
   *
   * @type {State}
   */
  function cdataEnd(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {
      return end(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {
      effects.consume(code)
      return cdataEnd
    }

    return cdata(code)
  }

  /**
   * In declaration.
   *
   * ```markdown
   * > | a <!b> c
   *          ^
   * ```
   *
   * @type {State}
   */
  function declaration(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {
      return end(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = declaration
      return lineEndingBefore(code)
    }

    effects.consume(code)
    return declaration
  }

  /**
   * In instruction.
   *
   * ```markdown
   * > | a <?b?> c
   *         ^
   * ```
   *
   * @type {State}
   */
  function instruction(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {
      return nok(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.questionMark) {
      effects.consume(code)
      return instructionClose
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = instruction
      return lineEndingBefore(code)
    }

    effects.consume(code)
    return instruction
  }

  /**
   * In instruction, after `?`, at `>`.
   *
   * ```markdown
   * > | a <?b?> c
   *           ^
   * ```
   *
   * @type {State}
   */
  function instructionClose(code) {
    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ? end(code) : instruction(code)
  }

  /**
   * After `</`, in closing tag, at tag name.
   *
   * ```markdown
   * > | a </b> c
   *         ^
   * ```
   *
   * @type {State}
   */
  function tagCloseStart(code) {
    // ASCII alphabetical.
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {
      effects.consume(code)
      return tagClose
    }

    return nok(code)
  }

  /**
   * After `</x`, in a tag name.
   *
   * ```markdown
   * > | a </b> c
   *          ^
   * ```
   *
   * @type {State}
   */
  function tagClose(code) {
    // ASCII alphanumerical and `-`.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) {
      effects.consume(code)
      return tagClose
    }

    return tagCloseBetween(code)
  }

  /**
   * In closing tag, after tag name.
   *
   * ```markdown
   * > | a </b> c
   *          ^
   * ```
   *
   * @type {State}
   */
  function tagCloseBetween(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = tagCloseBetween
      return lineEndingBefore(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      effects.consume(code)
      return tagCloseBetween
    }

    return end(code)
  }

  /**
   * After `<x`, in opening tag name.
   *
   * ```markdown
   * > | a <b> c
   *         ^
   * ```
   *
   * @type {State}
   */
  function tagOpen(code) {
    // ASCII alphanumerical and `-`.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) {
      effects.consume(code)
      return tagOpen
    }

    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)
    ) {
      return tagOpenBetween(code)
    }

    return nok(code)
  }

  /**
   * In opening tag, after tag name.
   *
   * ```markdown
   * > | a <b> c
   *         ^
   * ```
   *
   * @type {State}
   */
  function tagOpenBetween(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {
      effects.consume(code)
      return end
    }

    // ASCII alphabetical and `:` and `_`.
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {
      effects.consume(code)
      return tagOpenAttributeName
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = tagOpenBetween
      return lineEndingBefore(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      effects.consume(code)
      return tagOpenBetween
    }

    return end(code)
  }

  /**
   * In attribute name.
   *
   * ```markdown
   * > | a <b c> d
   *          ^
   * ```
   *
   * @type {State}
   */
  function tagOpenAttributeName(code) {
    // ASCII alphabetical and `-`, `.`, `:`, and `_`.
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.underscore ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)
    ) {
      effects.consume(code)
      return tagOpenAttributeName
    }

    return tagOpenAttributeNameAfter(code)
  }

  /**
   * After attribute name, before initializer, the end of the tag, or
   * whitespace.
   *
   * ```markdown
   * > | a <b c> d
   *           ^
   * ```
   *
   * @type {State}
   */
  function tagOpenAttributeNameAfter(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo) {
      effects.consume(code)
      return tagOpenAttributeValueBefore
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = tagOpenAttributeNameAfter
      return lineEndingBefore(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      effects.consume(code)
      return tagOpenAttributeNameAfter
    }

    return tagOpenBetween(code)
  }

  /**
   * Before unquoted, double quoted, or single quoted attribute value, allowing
   * whitespace.
   *
   * ```markdown
   * > | a <b c=d> e
   *            ^
   * ```
   *
   * @type {State}
   */
  function tagOpenAttributeValueBefore(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent
    ) {
      return nok(code)
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe) {
      effects.consume(code)
      marker = code
      return tagOpenAttributeValueQuoted
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = tagOpenAttributeValueBefore
      return lineEndingBefore(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      effects.consume(code)
      return tagOpenAttributeValueBefore
    }

    effects.consume(code)
    return tagOpenAttributeValueUnquoted
  }

  /**
   * In double or single quoted attribute value.
   *
   * ```markdown
   * > | a <b c="d"> e
   *             ^
   * ```
   *
   * @type {State}
   */
  function tagOpenAttributeValueQuoted(code) {
    if (code === marker) {
      effects.consume(code)
      marker = undefined
      return tagOpenAttributeValueQuotedAfter
    }

    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {
      return nok(code)
    }

    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      returnState = tagOpenAttributeValueQuoted
      return lineEndingBefore(code)
    }

    effects.consume(code)
    return tagOpenAttributeValueQuoted
  }

  /**
   * In unquoted attribute value.
   *
   * ```markdown
   * > | a <b c=d> e
   *            ^
   * ```
   *
   * @type {State}
   */
  function tagOpenAttributeValueUnquoted(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent
    ) {
      return nok(code)
    }

    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)
    ) {
      return tagOpenBetween(code)
    }

    effects.consume(code)
    return tagOpenAttributeValueUnquoted
  }

  /**
   * After double or single quoted attribute value, before whitespace or the end
   * of the tag.
   *
   * ```markdown
   * > | a <b c="d"> e
   *               ^
   * ```
   *
   * @type {State}
   */
  function tagOpenAttributeValueQuotedAfter(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||
      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)
    ) {
      return tagOpenBetween(code)
    }

    return nok(code)
  }

  /**
   * In certain circumstances of a tag where only an `>` is allowed.
   *
   * ```markdown
   * > | a <b c="d"> e
   *               ^
   * ```
   *
   * @type {State}
   */
  function end(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlText)
      return ok
    }

    return nok(code)
  }

  /**
   * At eol.
   *
   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about
   * > empty tokens.
   *
   * ```markdown
   * > | a <!--a
   *            ^
   *   | b-->
   * ```
   *
   * @type {State}
   */
  function lineEndingBefore(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(returnState, 'expected return state')
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected eol')
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    return lineEndingAfter
  }

  /**
   * After eol, at optional whitespace.
   *
   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about
   * > empty tokens.
   *
   * ```markdown
   *   | a <!--a
   * > | b-->
   *     ^
   * ```
   *
   * @type {State}
   */
  function lineEndingAfter(code) {
    // Always populated by defaults.
    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(
      self.parser.constructs.disable.null,
      'expected `disable.null` to be populated'
    )
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(
          effects,
          lineEndingAfterPrefix,
          micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,
          self.parser.constructs.disable.null.includes('codeIndented')
            ? undefined
            : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize
        )(code)
      : lineEndingAfterPrefix(code)
  }

  /**
   * After eol, after optional whitespace.
   *
   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about
   * > empty tokens.
   *
   * ```markdown
   *   | a <!--a
   * > | b-->
   *     ^
   * ```
   *
   * @type {State}
   */
  function lineEndingAfterPrefix(code) {
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)
    return returnState(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/label-end.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   labelEnd: () => (/* binding */ labelEnd)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_destination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-factory-destination */ "(ssr)/./node_modules/micromark-factory-destination/dev/index.js");
/* harmony import */ var micromark_factory_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-factory-label */ "(ssr)/./node_modules/micromark-factory-label/dev/index.js");
/* harmony import */ var micromark_factory_title__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-factory-title */ "(ssr)/./node_modules/micromark-factory-title/dev/index.js");
/* harmony import */ var micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-whitespace */ "(ssr)/./node_modules/micromark-factory-whitespace/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-chunked */ "(ssr)/./node_modules/micromark-util-chunked/dev/index.js");
/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-normalize-identifier */ "(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js");
/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ "(ssr)/./node_modules/micromark-util-resolve-all/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Construct,
 *   Event,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer,
 *   Token
 * } from 'micromark-util-types'
 */












/** @type {Construct} */
const labelEnd = {
  name: 'labelEnd',
  resolveAll: resolveAllLabelEnd,
  resolveTo: resolveToLabelEnd,
  tokenize: tokenizeLabelEnd
}

/** @type {Construct} */
const resourceConstruct = {tokenize: tokenizeResource}
/** @type {Construct} */
const referenceFullConstruct = {tokenize: tokenizeReferenceFull}
/** @type {Construct} */
const referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}

/** @type {Resolver} */
function resolveAllLabelEnd(events) {
  let index = -1
  /** @type {Array<Event>} */
  const newEvents = []
  while (++index < events.length) {
    const token = events[index][1]
    newEvents.push(events[index])

    if (
      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage ||
      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink ||
      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd
    ) {
      // Remove the marker.
      const offset = token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage ? 4 : 2
      token.type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data
      index += offset
    }
  }

  // If the events are equal, we don't have to copy newEvents to events
  if (events.length !== newEvents.length) {
    (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, 0, events.length, newEvents)
  }

  return events
}

/** @type {Resolver} */
function resolveToLabelEnd(events, context) {
  let index = events.length
  let offset = 0
  /** @type {Token} */
  let token
  /** @type {number | undefined} */
  let open
  /** @type {number | undefined} */
  let close
  /** @type {Array<Event>} */
  let media

  // Find an opening.
  while (index--) {
    token = events[index][1]

    if (open) {
      // If we see another link, or inactive link label, we’ve been here before.
      if (
        token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.link ||
        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink && token._inactive)
      ) {
        break
      }

      // Mark other link openings as inactive, as we can’t have links in
      // links.
      if (events[index][0] === 'enter' && token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) {
        token._inactive = true
      }
    } else if (close) {
      if (
        events[index][0] === 'enter' &&
        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage || token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) &&
        !token._balanced
      ) {
        open = index

        if (token.type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) {
          offset = 2
          break
        }
      }
    } else if (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd) {
      close = index
    }
  }

  (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(open !== undefined, '`open` is supposed to be found')
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(close !== undefined, '`close` is supposed to be found')

  const group = {
    type: events[open][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.link : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.image,
    start: {...events[open][1].start},
    end: {...events[events.length - 1][1].end}
  }

  const label = {
    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.label,
    start: {...events[open][1].start},
    end: {...events[close][1].end}
  }

  const text = {
    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelText,
    start: {...events[open + offset + 2][1].end},
    end: {...events[close - 2][1].start}
  }

  media = [
    ['enter', group, context],
    ['enter', label, context]
  ]

  // Opening marker.
  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, events.slice(open + 1, open + offset + 3))

  // Text open.
  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, [['enter', text, context]])

  // Always populated by defaults.
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(
    context.parser.constructs.insideSpan.null,
    'expected `insideSpan.null` to be populated'
  )
  // Between.
  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(
    media,
    (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(
      context.parser.constructs.insideSpan.null,
      events.slice(open + offset + 4, close - 3),
      context
    )
  )

  // Text close, marker close, label close.
  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, [
    ['exit', text, context],
    events[close - 2],
    events[close - 1],
    ['exit', label, context]
  ])

  // Reference, resource, or so.
  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, events.slice(close + 1))

  // Media close.
  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, [['exit', group, context]])

  ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, open, events.length, media)

  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeLabelEnd(effects, ok, nok) {
  const self = this
  let index = self.events.length
  /** @type {Token} */
  let labelStart
  /** @type {boolean} */
  let defined

  // Find an opening.
  while (index--) {
    if (
      (self.events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage ||
        self.events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) &&
      !self.events[index][1]._balanced
    ) {
      labelStart = self.events[index][1]
      break
    }
  }

  return start

  /**
   * Start of label end.
   *
   * ```markdown
   * > | [a](b) c
   *       ^
   * > | [a][b] c
   *       ^
   * > | [a][] b
   *       ^
   * > | [a] b
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightSquareBracket, 'expected `]`')

    // If there is not an okay opening.
    if (!labelStart) {
      return nok(code)
    }

    // If the corresponding label (link) start is marked as inactive,
    // it means we’d be wrapping a link, like this:
    //
    // ```markdown
    // > | a [b [c](d) e](f) g.
    //                  ^
    // ```
    //
    // We can’t have that, so it’s just balanced brackets.
    if (labelStart._inactive) {
      return labelEndNok(code)
    }

    defined = self.parser.defined.includes(
      (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__.normalizeIdentifier)(
        self.sliceSerialize({start: labelStart.end, end: self.now()})
      )
    )
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelMarker)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd)
    return after
  }

  /**
   * After `]`.
   *
   * ```markdown
   * > | [a](b) c
   *       ^
   * > | [a][b] c
   *       ^
   * > | [a][] b
   *       ^
   * > | [a] b
   *       ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in
    // an extension.

    // Resource (`[asd](fgh)`)?
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis) {
      return effects.attempt(
        resourceConstruct,
        labelEndOk,
        defined ? labelEndOk : labelEndNok
      )(code)
    }

    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket) {
      return effects.attempt(
        referenceFullConstruct,
        labelEndOk,
        defined ? referenceNotFull : labelEndNok
      )(code)
    }

    // Shortcut (`[asd]`) reference?
    return defined ? labelEndOk(code) : labelEndNok(code)
  }

  /**
   * After `]`, at `[`, but not at a full reference.
   *
   * > 👉 **Note**: we only get here if the label is defined.
   *
   * ```markdown
   * > | [a][] b
   *        ^
   * > | [a] b
   *        ^
   * ```
   *
   * @type {State}
   */
  function referenceNotFull(code) {
    return effects.attempt(
      referenceCollapsedConstruct,
      labelEndOk,
      labelEndNok
    )(code)
  }

  /**
   * Done, we found something.
   *
   * ```markdown
   * > | [a](b) c
   *           ^
   * > | [a][b] c
   *           ^
   * > | [a][] b
   *          ^
   * > | [a] b
   *        ^
   * ```
   *
   * @type {State}
   */
  function labelEndOk(code) {
    // Note: `markdown-rs` does a bunch of stuff here.
    return ok(code)
  }

  /**
   * Done, it’s nothing.
   *
   * There was an okay opening, but we didn’t match anything.
   *
   * ```markdown
   * > | [a](b c
   *        ^
   * > | [a][b c
   *        ^
   * > | [a] b
   *        ^
   * ```
   *
   * @type {State}
   */
  function labelEndNok(code) {
    labelStart._balanced = true
    return nok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeResource(effects, ok, nok) {
  return resourceStart

  /**
   * At a resource.
   *
   * ```markdown
   * > | [a](b) c
   *        ^
   * ```
   *
   * @type {State}
   */
  function resourceStart(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis, 'expected left paren')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resource)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)
    return resourceBefore
  }

  /**
   * In resource, after `(`, at optional whitespace.
   *
   * ```markdown
   * > | [a](b) c
   *         ^
   * ```
   *
   * @type {State}
   */
  function resourceBefore(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)
      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceOpen)(code)
      : resourceOpen(code)
  }

  /**
   * In resource, after optional whitespace, at `)` or a destination.
   *
   * ```markdown
   * > | [a](b) c
   *         ^
   * ```
   *
   * @type {State}
   */
  function resourceOpen(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightParenthesis) {
      return resourceEnd(code)
    }

    return (0,micromark_factory_destination__WEBPACK_IMPORTED_MODULE_8__.factoryDestination)(
      effects,
      resourceDestinationAfter,
      resourceDestinationMissing,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestination,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationLiteral,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationLiteralMarker,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationRaw,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationString,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__.constants.linkResourceDestinationBalanceMax
    )(code)
  }

  /**
   * In resource, after destination, at optional whitespace.
   *
   * ```markdown
   * > | [a](b) c
   *          ^
   * ```
   *
   * @type {State}
   */
  function resourceDestinationAfter(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)
      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceBetween)(code)
      : resourceEnd(code)
  }

  /**
   * At invalid destination.
   *
   * ```markdown
   * > | [a](<<) b
   *         ^
   * ```
   *
   * @type {State}
   */
  function resourceDestinationMissing(code) {
    return nok(code)
  }

  /**
   * In resource, after destination and whitespace, at `(` or title.
   *
   * ```markdown
   * > | [a](b ) c
   *           ^
   * ```
   *
   * @type {State}
   */
  function resourceBetween(code) {
    if (
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.quotationMark ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.apostrophe ||
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis
    ) {
      return (0,micromark_factory_title__WEBPACK_IMPORTED_MODULE_10__.factoryTitle)(
        effects,
        resourceTitleAfter,
        nok,
        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceTitle,
        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceTitleMarker,
        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceTitleString
      )(code)
    }

    return resourceEnd(code)
  }

  /**
   * In resource, after title, at optional whitespace.
   *
   * ```markdown
   * > | [a](b "c") d
   *              ^
   * ```
   *
   * @type {State}
   */
  function resourceTitleAfter(code) {
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)
      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceEnd)(code)
      : resourceEnd(code)
  }

  /**
   * In resource, at `)`.
   *
   * ```markdown
   * > | [a](b) d
   *          ^
   * ```
   *
   * @type {State}
   */
  function resourceEnd(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightParenthesis) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resource)
      return ok
    }

    return nok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeReferenceFull(effects, ok, nok) {
  const self = this

  return referenceFull

  /**
   * In a reference (full), at the `[`.
   *
   * ```markdown
   * > | [a][b] d
   *        ^
   * ```
   *
   * @type {State}
   */
  function referenceFull(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket, 'expected left bracket')
    return micromark_factory_label__WEBPACK_IMPORTED_MODULE_11__.factoryLabel.call(
      self,
      effects,
      referenceFullAfter,
      referenceFullMissing,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.reference,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceString
    )(code)
  }

  /**
   * In a reference (full), after `]`.
   *
   * ```markdown
   * > | [a][b] d
   *          ^
   * ```
   *
   * @type {State}
   */
  function referenceFullAfter(code) {
    return self.parser.defined.includes(
      (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__.normalizeIdentifier)(
        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)
      )
    )
      ? ok(code)
      : nok(code)
  }

  /**
   * In reference (full) that was missing.
   *
   * ```markdown
   * > | [a][b d
   *        ^
   * ```
   *
   * @type {State}
   */
  function referenceFullMissing(code) {
    return nok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeReferenceCollapsed(effects, ok, nok) {
  return referenceCollapsedStart

  /**
   * In reference (collapsed), at `[`.
   *
   * > 👉 **Note**: we only get here if the label is defined.
   *
   * ```markdown
   * > | [a][] d
   *        ^
   * ```
   *
   * @type {State}
   */
  function referenceCollapsedStart(code) {
    // We only attempt a collapsed label if there’s a `[`.
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket, 'expected left bracket')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.reference)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)
    return referenceCollapsedOpen
  }

  /**
   * In reference (collapsed), at `]`.
   *
   * > 👉 **Note**: we only get here if the label is defined.
   *
   * ```markdown
   * > | [a][] d
   *         ^
   * ```
   *
   *  @type {State}
   */
  function referenceCollapsedOpen(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightSquareBracket) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.reference)
      return ok
    }

    return nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   labelStartImage: () => (/* binding */ labelStartImage)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var _label_end_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./label-end.js */ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const labelStartImage = {
  name: 'labelStartImage',
  resolveAll: _label_end_js__WEBPACK_IMPORTED_MODULE_0__.labelEnd.resolveAll,
  tokenize: tokenizeLabelStartImage
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeLabelStartImage(effects, ok, nok) {
  const self = this

  return start

  /**
   * Start of label (image) start.
   *
   * ```markdown
   * > | a ![b] c
   *       ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.exclamationMark, 'expected `!`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImage)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImageMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImageMarker)
    return open
  }

  /**
   * After `!`, at `[`.
   *
   * ```markdown
   * > | a ![b] c
   *        ^
   * ```
   *
   * @type {State}
   */
  function open(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImage)
      return after
    }

    return nok(code)
  }

  /**
   * After `![`.
   *
   * ```markdown
   * > | a ![b] c
   *         ^
   * ```
   *
   * This is needed in because, when GFM footnotes are enabled, images never
   * form when started with a `^`.
   * Instead, links form:
   *
   * ```markdown
   * ![^a](b)
   *
   * ![^a][b]
   *
   * [b]: c
   * ```
   *
   * ```html
   * <p>!<a href=\"b\">^a</a></p>
   * <p>!<a href=\"c\">^a</a></p>
   * ```
   *
   * @type {State}
   */
  function after(code) {
    // To do: use a new field to do this, this is still needed for
    // `micromark-extension-gfm-footnote`, but the `label-start-link`
    // behavior isn’t.
    // Hidden footnotes hook.
    /* c8 ignore next 3 */
    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.caret &&
      '_hiddenFootnoteSupport' in self.parser.constructs
      ? nok(code)
      : ok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   labelStartLink: () => (/* binding */ labelStartLink)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var _label_end_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./label-end.js */ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */





/** @type {Construct} */
const labelStartLink = {
  name: 'labelStartLink',
  resolveAll: _label_end_js__WEBPACK_IMPORTED_MODULE_0__.labelEnd.resolveAll,
  tokenize: tokenizeLabelStartLink
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeLabelStartLink(effects, ok, nok) {
  const self = this

  return start

  /**
   * Start of label (link) start.
   *
   * ```markdown
   * > | a [b] c
   *       ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket, 'expected `[`')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelLink)
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelLink)
    return after
  }

  /** @type {State} */
  function after(code) {
    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,
    // remove.
    // Hidden footnotes hook.
    /* c8 ignore next 3 */
    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.caret &&
      '_hiddenFootnoteSupport' in self.parser.constructs
      ? nok(code)
      : ok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/line-ending.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   lineEnding: () => (/* binding */ lineEnding)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/**
 * @import {
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeLineEnding(effects, ok) {
  return start

  /** @type {State} */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code), 'expected eol')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)
    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, ok, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   list: () => (/* binding */ list)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/* harmony import */ var _blank_line_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blank-line.js */ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js");
/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./thematic-break.js */ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   Exiter,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */








/** @type {Construct} */
const list = {
  continuation: {tokenize: tokenizeListContinuation},
  exit: tokenizeListEnd,
  name: 'list',
  tokenize: tokenizeListStart
}

/** @type {Construct} */
const listItemPrefixWhitespaceConstruct = {
  partial: true,
  tokenize: tokenizeListItemPrefixWhitespace
}

/** @type {Construct} */
const indentConstruct = {partial: true, tokenize: tokenizeIndent}

// To do: `markdown-rs` parses list items on their own and later stitches them
// together.

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeListStart(effects, ok, nok) {
  const self = this
  const tail = self.events[self.events.length - 1]
  let initialSize =
    tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix
      ? tail[2].sliceSerialize(tail[1], true).length
      : 0
  let size = 0

  return start

  /** @type {State} */
  function start(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    const kind =
      self.containerState.type ||
      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.plusSign || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash
        ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listUnordered
        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listOrdered)

    if (
      kind === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listUnordered
        ? !self.containerState.marker || code === self.containerState.marker
        : (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiDigit)(code)
    ) {
      if (!self.containerState.type) {
        self.containerState.type = kind
        effects.enter(kind, {_container: true})
      }

      if (kind === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listUnordered) {
        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefix)
        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash
          ? effects.check(_thematic_break_js__WEBPACK_IMPORTED_MODULE_4__.thematicBreak, nok, atMarker)(code)
          : atMarker(code)
      }

      if (!self.interrupt || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.digit1) {
        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefix)
        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemValue)
        return inside(code)
      }
    }

    return nok(code)
  }

  /** @type {State} */
  function inside(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiDigit)(code) && ++size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.listItemValueSizeMax) {
      effects.consume(code)
      return inside
    }

    if (
      (!self.interrupt || size < 2) &&
      (self.containerState.marker
        ? code === self.containerState.marker
        : code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.rightParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dot)
    ) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemValue)
      return atMarker(code)
    }

    return nok(code)
  }

  /**
   * @type {State}
   **/
  function atMarker(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof, 'eof (`null`) is not a marker')
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemMarker)
    effects.consume(code)
    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemMarker)
    self.containerState.marker = self.containerState.marker || code
    return effects.check(
      _blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine,
      // Can’t be empty when interrupting.
      self.interrupt ? nok : onBlank,
      effects.attempt(
        listItemPrefixWhitespaceConstruct,
        endOfPrefix,
        otherPrefix
      )
    )
  }

  /** @type {State} */
  function onBlank(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    self.containerState.initialBlankLine = true
    initialSize++
    return endOfPrefix(code)
  }

  /** @type {State} */
  function otherPrefix(code) {
    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace)
      effects.consume(code)
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace)
      return endOfPrefix
    }

    return nok(code)
  }

  /** @type {State} */
  function endOfPrefix(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    self.containerState.size =
      initialSize +
      self.sliceSerialize(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefix), true).length
    return ok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeListContinuation(effects, ok, nok) {
  const self = this

  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
  self.containerState._closeFlow = undefined

  return effects.check(_blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine, onBlank, notBlank)

  /** @type {State} */
  function onBlank(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof self.containerState.size === 'number', 'expected size')
    self.containerState.furtherBlankLines =
      self.containerState.furtherBlankLines ||
      self.containerState.initialBlankLine

    // We have a blank line.
    // Still, try to consume at most the items size.
    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(
      effects,
      ok,
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemIndent,
      self.containerState.size + 1
    )(code)
  }

  /** @type {State} */
  function notBlank(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    if (self.containerState.furtherBlankLines || !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {
      self.containerState.furtherBlankLines = undefined
      self.containerState.initialBlankLine = undefined
      return notInCurrentItem(code)
    }

    self.containerState.furtherBlankLines = undefined
    self.containerState.initialBlankLine = undefined
    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)
  }

  /** @type {State} */
  function notInCurrentItem(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    // While we do continue, we signal that the flow should be closed.
    self.containerState._closeFlow = true
    // As we’re closing flow, we’re no longer interrupting.
    self.interrupt = undefined
    // Always populated by defaults.
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
      self.parser.constructs.disable.null,
      'expected `disable.null` to be populated'
    )
    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(
      effects,
      effects.attempt(list, ok, nok),
      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,
      self.parser.constructs.disable.null.includes('codeIndented')
        ? undefined
        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize
    )(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeIndent(effects, ok, nok) {
  const self = this

  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof self.containerState.size === 'number', 'expected size')

  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(
    effects,
    afterPrefix,
    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemIndent,
    self.containerState.size + 1
  )

  /** @type {State} */
  function afterPrefix(code) {
    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')
    const tail = self.events[self.events.length - 1]
    return tail &&
      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemIndent &&
      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size
      ? ok(code)
      : nok(code)
  }
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Exiter}
 */
function tokenizeListEnd(effects) {
  (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(this.containerState, 'expected state')
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof this.containerState.type === 'string', 'expected type')
  effects.exit(this.containerState.type)
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeListItemPrefixWhitespace(effects, ok, nok) {
  const self = this

  // Always populated by defaults.
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
    self.parser.constructs.disable.null,
    'expected `disable.null` to be populated'
  )

  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(
    effects,
    afterPrefix,
    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace,
    self.parser.constructs.disable.null.includes('codeIndented')
      ? undefined
      : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize + 1
  )

  /** @type {State} */
  function afterPrefix(code) {
    const tail = self.events[self.events.length - 1]

    return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code) &&
      tail &&
      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace
      ? ok(code)
      : nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   setextUnderline: () => (/* binding */ setextUnderline)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   Resolver,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const setextUnderline = {
  name: 'setextUnderline',
  resolveTo: resolveToSetextUnderline,
  tokenize: tokenizeSetextUnderline
}

/** @type {Resolver} */
function resolveToSetextUnderline(events, context) {
  // To do: resolve like `markdown-rs`.
  let index = events.length
  /** @type {number | undefined} */
  let content
  /** @type {number | undefined} */
  let text
  /** @type {number | undefined} */
  let definition

  // Find the opening of the content.
  // It’ll always exist: we don’t tokenize if it isn’t there.
  while (index--) {
    if (events[index][0] === 'enter') {
      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.content) {
        content = index
        break
      }

      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.paragraph) {
        text = index
      }
    }
    // Exit
    else {
      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.content) {
        // Remove the content end (if needed we’ll add it later)
        events.splice(index, 1)
      }

      if (!definition && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definition) {
        definition = index
      }
    }
  }

  (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(text !== undefined, 'expected a `text` index to be found')
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(content !== undefined, 'expected a `text` index to be found')
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(events[content][2] === context, 'enter context should be same')
  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
    events[events.length - 1][2] === context,
    'enter context should be same'
  )
  const heading = {
    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeading,
    start: {...events[content][1].start},
    end: {...events[events.length - 1][1].end}
  }

  // Change the paragraph to setext heading text.
  events[text][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingText

  // If we have definitions in the content, we’ll keep on having content,
  // but we need move it.
  if (definition) {
    events.splice(text, 0, ['enter', heading, context])
    events.splice(definition + 1, 0, ['exit', events[content][1], context])
    events[content][1].end = {...events[definition][1].end}
  } else {
    events[content][1] = heading
  }

  // Add the heading exit at the end.
  events.push(['exit', heading, context])

  return events
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeSetextUnderline(effects, ok, nok) {
  const self = this
  /** @type {NonNullable<Code>} */
  let marker

  return start

  /**
   * At start of heading (setext) underline.
   *
   * ```markdown
   *   | aa
   * > | ==
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    let index = self.events.length
    /** @type {boolean | undefined} */
    let paragraph

    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo,
      'expected `=` or `-`'
    )

    // Find an opening.
    while (index--) {
      // Skip enter/exit of line ending, line prefix, and content.
      // We can now either have a definition or a paragraph.
      if (
        self.events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding &&
        self.events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix &&
        self.events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.content
      ) {
        paragraph = self.events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.paragraph
        break
      }
    }

    // To do: handle lazy/pierce like `markdown-rs`.
    // To do: parse indent like `markdown-rs`.
    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLine)
      marker = code
      return before(code)
    }

    return nok(code)
  }

  /**
   * After optional whitespace, at `-` or `=`.
   *
   * ```markdown
   *   | aa
   * > | ==
   *     ^
   * ```
   *
   * @type {State}
   */
  function before(code) {
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLineSequence)
    return inside(code)
  }

  /**
   * In sequence.
   *
   * ```markdown
   *   | aa
   * > | ==
   *     ^
   * ```
   *
   * @type {State}
   */
  function inside(code) {
    if (code === marker) {
      effects.consume(code)
      return inside
    }

    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLineSequence)

    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineSuffix)(code)
      : after(code)
  }

  /**
   * After sequence, after optional whitespace.
   *
   * ```markdown
   *   | aa
   * > | ==
   *       ^
   * ```
   *
   * @type {State}
   */
  function after(code) {
    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLine)
      return ok(code)
    }

    return nok(code)
  }
}


/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)
/* harmony export */ });
/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ "(ssr)/./node_modules/devlop/lib/development.js");
/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ "(ssr)/./node_modules/micromark-factory-space/dev/index.js");
/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ "(ssr)/./node_modules/micromark-util-character/dev/index.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js");
/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js");
/**
 * @import {
 *   Code,
 *   Construct,
 *   State,
 *   TokenizeContext,
 *   Tokenizer
 * } from 'micromark-util-types'
 */






/** @type {Construct} */
const thematicBreak = {
  name: 'thematicBreak',
  tokenize: tokenizeThematicBreak
}

/**
 * @this {TokenizeContext}
 *   Context.
 * @type {Tokenizer}
 */
function tokenizeThematicBreak(effects, ok, nok) {
  let size = 0
  /** @type {NonNullable<Code>} */
  let marker

  return start

  /**
   * Start of thematic break.
   *
   * ```markdown
   * > | ***
   *     ^
   * ```
   *
   * @type {State}
   */
  function start(code) {
    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreak)
    // To do: parse indent like `markdown-rs`.
    return before(code)
  }

  /**
   * After optional whitespace, at marker.
   *
   * ```markdown
   * > | ***
   *     ^
   * ```
   *
   * @type {State}
   */
  function before(code) {
    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(
      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk ||
        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash ||
        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.underscore,
      'expected `*`, `-`, or `_`'
    )
    marker = code
    return atBreak(code)
  }

  /**
   * After something, before something else.
   *
   * ```markdown
   * > | ***
   *     ^
   * ```
   *
   * @type {State}
   */
  function atBreak(code) {
    if (code === marker) {
      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreakSequence)
      return sequence(code)
    }

    if (
      size >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.thematicBreakMarkerCountMin &&
      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code))
    ) {
      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreak)
      return ok(code)
    }

    return nok(code)
  }

  /**
   * In sequence.
   *
   * ```markdown
   * > | ***
   *     ^
   * ```
   *
   * @type {State}
   */
  function sequence(code) {
    if (code === marker) {
      effects.consume(code)
      size++
      return sequence
    }

    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreakSequence)
    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)
      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, atBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)
      : atBreak(code)
  }
}


/***/ })

};
;