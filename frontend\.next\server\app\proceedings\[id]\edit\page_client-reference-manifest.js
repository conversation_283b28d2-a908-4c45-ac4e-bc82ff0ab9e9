globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/proceedings/[id]/edit/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/layout.tsx":{"*":{"id":"(ssr)/./app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/loading.tsx":{"*":{"id":"(ssr)/./app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/documents/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./app/documents/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/documents/page.tsx":{"*":{"id":"(ssr)/./app/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/documents/[id]/page.tsx":{"*":{"id":"(ssr)/./app/documents/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/search/page.tsx":{"*":{"id":"(ssr)/./app/search/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/regulations/page.tsx":{"*":{"id":"(ssr)/./app/regulations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/agencies/page.tsx":{"*":{"id":"(ssr)/./app/agencies/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/agencies/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./app/agencies/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/categories/page.tsx":{"*":{"id":"(ssr)/./app/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/agencies/[id]/page.tsx":{"*":{"id":"(ssr)/./app/agencies/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/categories/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./app/categories/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/categories/[id]/page.tsx":{"*":{"id":"(ssr)/./app/categories/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/finance/page.tsx":{"*":{"id":"(ssr)/./app/finance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/regulations/full/page.tsx":{"*":{"id":"(ssr)/./app/regulations/full/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/proceedings/page.tsx":{"*":{"id":"(ssr)/./app/proceedings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/proceedings/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./app/proceedings/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/proceedings/[id]/page.tsx":{"*":{"id":"(ssr)/./app/proceedings/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\layout.tsx":{"id":"(app-pages-browser)/./app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\loading.tsx":{"id":"(app-pages-browser)/./app/loading.tsx","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\documents\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./app/documents/[id]/edit/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\documents\\page.tsx":{"id":"(app-pages-browser)/./app/documents/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\documents\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/documents/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\search\\page.tsx":{"id":"(app-pages-browser)/./app/search/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\regulations\\page.tsx":{"id":"(app-pages-browser)/./app/regulations/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\agencies\\page.tsx":{"id":"(app-pages-browser)/./app/agencies/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\agencies\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./app/agencies/[id]/edit/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\categories\\page.tsx":{"id":"(app-pages-browser)/./app/categories/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\agencies\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/agencies/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\categories\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./app/categories/[id]/edit/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\categories\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/categories/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\finance\\page.tsx":{"id":"(app-pages-browser)/./app/finance/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\regulations\\full\\page.tsx":{"id":"(app-pages-browser)/./app/regulations/full/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\proceedings\\page.tsx":{"id":"(app-pages-browser)/./app/proceedings/page.tsx","name":"*","chunks":["app/proceedings/page","static/chunks/app/proceedings/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\proceedings\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./app/proceedings/[id]/edit/page.tsx","name":"*","chunks":["app/proceedings/[id]/edit/page","static/chunks/app/proceedings/%5Bid%5D/edit/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\proceedings\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/proceedings/[id]/page.tsx","name":"*","chunks":["app/proceedings/[id]/page","static/chunks/app/proceedings/%5Bid%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\error":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\loading":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\not-found":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\proceedings\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\proceedings\\[id]\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\NoteControl\\frontend\\app\\proceedings\\[id]\\edit\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/layout.tsx":{"*":{"id":"(rsc)/./app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(rsc)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/loading.tsx":{"*":{"id":"(rsc)/./app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(rsc)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/documents/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./app/documents/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/documents/page.tsx":{"*":{"id":"(rsc)/./app/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/documents/[id]/page.tsx":{"*":{"id":"(rsc)/./app/documents/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/search/page.tsx":{"*":{"id":"(rsc)/./app/search/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/regulations/page.tsx":{"*":{"id":"(rsc)/./app/regulations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/agencies/page.tsx":{"*":{"id":"(rsc)/./app/agencies/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/agencies/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./app/agencies/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/categories/page.tsx":{"*":{"id":"(rsc)/./app/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/agencies/[id]/page.tsx":{"*":{"id":"(rsc)/./app/agencies/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/categories/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./app/categories/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/categories/[id]/page.tsx":{"*":{"id":"(rsc)/./app/categories/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/finance/page.tsx":{"*":{"id":"(rsc)/./app/finance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/regulations/full/page.tsx":{"*":{"id":"(rsc)/./app/regulations/full/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/proceedings/page.tsx":{"*":{"id":"(rsc)/./app/proceedings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/proceedings/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./app/proceedings/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/proceedings/[id]/page.tsx":{"*":{"id":"(rsc)/./app/proceedings/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}