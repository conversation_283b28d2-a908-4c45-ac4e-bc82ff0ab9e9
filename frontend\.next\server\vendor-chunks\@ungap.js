"use strict";
exports.id = "vendor-chunks/@ungap";
exports.ids = ["vendor-chunks/@ungap"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   deserialize: () => (/* binding */ deserialize)
/* harmony export */ });
/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js");


const env = typeof self === 'object' ? self : globalThis;

const deserializer = ($, _) => {
  const as = (out, index) => {
    $.set(index, out);
    return out;
  };

  const unpair = index => {
    if ($.has(index))
      return $.get(index);

    const [type, value] = _[index];
    switch (type) {
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID:
        return as(value, index);
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY: {
        const arr = as([], index);
        for (const index of value)
          arr.push(unpair(index));
        return arr;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT: {
        const object = as({}, index);
        for (const [key, index] of value)
          object[unpair(key)] = unpair(index);
        return object;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:
        return as(new Date(value), index);
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP: {
        const {source, flags} = value;
        return as(new RegExp(source, flags), index);
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP: {
        const map = as(new Map, index);
        for (const [key, index] of value)
          map.set(unpair(key), unpair(index));
        return map;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET: {
        const set = as(new Set, index);
        for (const index of value)
          set.add(unpair(index));
        return set;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR: {
        const {name, message} = value;
        return as(new env[name](message), index);
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT:
        return as(BigInt(value), index);
      case 'BigInt':
        return as(Object(BigInt(value)), index);
      case 'ArrayBuffer':
        return as(new Uint8Array(value).buffer, value);
      case 'DataView': {
        const { buffer } = new Uint8Array(value);
        return as(new DataView(buffer), value);
      }
    }
    return as(new env[type](value), index);
  };

  return unpair;
};

/**
 * @typedef {Array<string,any>} Record a type representation
 */

/**
 * Returns a deserialized value from a serialized array of Records.
 * @param {Record[]} serialized a previously serialized value.
 * @returns {any}
 */
const deserialize = serialized => deserializer(new Map, serialized)(0);


/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   deserialize: () => (/* reexport safe */ _deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize),
/* harmony export */   serialize: () => (/* reexport safe */ _serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)
/* harmony export */ });
/* harmony import */ var _deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserialize.js */ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js");
/* harmony import */ var _serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serialize.js */ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js");



/**
 * @typedef {Array<string,any>} Record a type representation
 */

/**
 * Returns an array of serialized Records.
 * @param {any} any a serializable value.
 * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with
 * a transfer option (ignored when polyfilled) and/or non standard fields that
 * fallback to the polyfill if present.
 * @returns {Record[]}
 */
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (typeof structuredClone === "function" ?
  /* c8 ignore start */
  (any, options) => (
    options && ('json' in options || 'lossy' in options) ?
      (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)) : structuredClone(any)
  ) :
  (any, options) => (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)));
  /* c8 ignore stop */




/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   serialize: () => (/* binding */ serialize)
/* harmony export */ });
/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js");


const EMPTY = '';

const {toString} = {};
const {keys} = Object;

const typeOf = value => {
  const type = typeof value;
  if (type !== 'object' || !value)
    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE, type];

  const asString = toString.call(value).slice(8, -1);
  switch (asString) {
    case 'Array':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, EMPTY];
    case 'Object':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT, EMPTY];
    case 'Date':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.DATE, EMPTY];
    case 'RegExp':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP, EMPTY];
    case 'Map':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.MAP, EMPTY];
    case 'Set':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.SET, EMPTY];
    case 'DataView':
      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, asString];
  }

  if (asString.includes('Array'))
    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, asString];

  if (asString.includes('Error'))
    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR, asString];

  return [_types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT, asString];
};

const shouldSkip = ([TYPE, type]) => (
  TYPE === _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE &&
  (type === 'function' || type === 'symbol')
);

const serializer = (strict, json, $, _) => {

  const as = (out, value) => {
    const index = _.push(out) - 1;
    $.set(value, index);
    return index;
  };

  const pair = value => {
    if ($.has(value))
      return $.get(value);

    let [TYPE, type] = typeOf(value);
    switch (TYPE) {
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE: {
        let entry = value;
        switch (type) {
          case 'bigint':
            TYPE = _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT;
            entry = value.toString();
            break;
          case 'function':
          case 'symbol':
            if (strict)
              throw new TypeError('unable to serialize ' + type);
            entry = null;
            break;
          case 'undefined':
            return as([_types_js__WEBPACK_IMPORTED_MODULE_0__.VOID], value);
        }
        return as([TYPE, entry], value);
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY: {
        if (type) {
          let spread = value;
          if (type === 'DataView') {
            spread = new Uint8Array(value.buffer);
          }
          else if (type === 'ArrayBuffer') {
            spread = new Uint8Array(value);
          }
          return as([type, [...spread]], value);
        }

        const arr = [];
        const index = as([TYPE, arr], value);
        for (const entry of value)
          arr.push(pair(entry));
        return index;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT: {
        if (type) {
          switch (type) {
            case 'BigInt':
              return as([type, value.toString()], value);
            case 'Boolean':
            case 'Number':
            case 'String':
              return as([type, value.valueOf()], value);
          }
        }

        if (json && ('toJSON' in value))
          return pair(value.toJSON());

        const entries = [];
        const index = as([TYPE, entries], value);
        for (const key of keys(value)) {
          if (strict || !shouldSkip(typeOf(value[key])))
            entries.push([pair(key), pair(value[key])]);
        }
        return index;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:
        return as([TYPE, value.toISOString()], value);
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP: {
        const {source, flags} = value;
        return as([TYPE, {source, flags}], value);
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP: {
        const entries = [];
        const index = as([TYPE, entries], value);
        for (const [key, entry] of value) {
          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))
            entries.push([pair(key), pair(entry)]);
        }
        return index;
      }
      case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET: {
        const entries = [];
        const index = as([TYPE, entries], value);
        for (const entry of value) {
          if (strict || !shouldSkip(typeOf(entry)))
            entries.push(pair(entry));
        }
        return index;
      }
    }

    const {message} = value;
    return as([TYPE, {name: type, message}], value);
  };

  return pair;
};

/**
 * @typedef {Array<string,any>} Record a type representation
 */

/**
 * Returns an array of serialized Records.
 * @param {any} value a serializable value.
 * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,
 *  if `true`, will not throw errors on incompatible types, and behave more
 *  like JSON stringify would behave. Symbol and Function will be discarded.
 * @returns {Record[]}
 */
 const serialize = (value, {json, lossy} = {}) => {
  const _ = [];
  return serializer(!(json || lossy), !!json, new Map, _)(value), _;
};


/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/types.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ARRAY: () => (/* binding */ ARRAY),
/* harmony export */   BIGINT: () => (/* binding */ BIGINT),
/* harmony export */   DATE: () => (/* binding */ DATE),
/* harmony export */   ERROR: () => (/* binding */ ERROR),
/* harmony export */   MAP: () => (/* binding */ MAP),
/* harmony export */   OBJECT: () => (/* binding */ OBJECT),
/* harmony export */   PRIMITIVE: () => (/* binding */ PRIMITIVE),
/* harmony export */   REGEXP: () => (/* binding */ REGEXP),
/* harmony export */   SET: () => (/* binding */ SET),
/* harmony export */   VOID: () => (/* binding */ VOID)
/* harmony export */ });
const VOID       = -1;
const PRIMITIVE  = 0;
const ARRAY      = 1;
const OBJECT     = 2;
const DATE       = 3;
const REGEXP     = 4;
const MAP        = 5;
const SET        = 6;
const ERROR      = 7;
const BIGINT     = 8;
// export const SYMBOL = 9;


/***/ })

};
;