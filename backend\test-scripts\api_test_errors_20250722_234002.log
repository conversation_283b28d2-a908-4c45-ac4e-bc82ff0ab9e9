=== API COMPREHENSIVE TEST ERROR LOG ===
Test started at: 2025-07-22 23:40:02
===================================================
[23:40:04] auth | POST /auth/reset-password | Status: 400 | Type: VALIDATION_ERROR | Critical: false
  Error: HTTP 400 error: {"error":"Invalid or expired reset token","message":"token is malformed: token contains an invalid number of segments"}
  Response: {"error":"Invalid or expired reset token","message":"token is malformed: token contains an invalid number of segments"}

[23:40:04] auth | POST /auth/change-password | Status: 400 | Type: VALIDATION_ERROR | Critical: false
  Error: HTTP 400 error: {"error":"Invalid current password","message":"The current password you entered is incorrect"}
  Response: {"error":"Invalid current password","message":"The current password you entered is incorrect"}

[23:40:06] agencies | CREATE /agencies | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:06] categories | CREATE /categories | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:07] documents | CREATE /documents | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:08] regulations | CREATE /regulations | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:10] proceedings | CREATE /proceedings | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:10] finance | CREATE /finance | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:11] finance_categories | CREATE /finance/categories | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:12] users | GET /users | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:12] users | CREATE /users | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:12] summaries | CREATE /summaries | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:13] calendar_events | CREATE /calendar/events | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:15] tags | CREATE /tags | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:16] subjects | CREATE /subjects | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:16] comments | CREATE /documents/150/comments | Status: 400 | Type: VALIDATION_ERROR | Critical: false
  Error: HTTP 400 error: {"error":"Comments not accepted","message":"This document does not accept comments"}
  Response: {"error":"Comments not accepted","message":"This document does not accept comments"}

[23:40:17] analytics_dashboard | GET /analytics/dashboard | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:17] analytics_documents | GET /analytics/documents/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:17] analytics_agencies | GET /analytics/agencies/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:17] analytics_users | GET /analytics/users/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:17] analytics_relationships | GET /analytics/relationships/stats | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:18] roles | GET /admin/roles | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:18] roles | CREATE /admin/roles | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:18] permissions | GET /admin/permissions | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:18] signatures | CREATE /documents | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:19] certificates | CREATE /certificates | Status: 500 | Type: SERVER_ERROR | Critical: true
  Error: HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create certificate: ERROR: column \"certificate_policies\" of relation \"digital_certificates\" does not exist (SQLSTATE 42703)"}
  Response: {"error":"Internal Server Error","message":"Failed to create certificate: ERROR: column \"certificate_policies\" of relation \"digital_certificates\" does not exist (SQLSTATE 42703)"}

[23:40:22] processing_jobs | CREATE /documents | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:24] enterprise_kpi | CREATE /enterprise/bi/kpis | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

[23:40:26] public | GET_NO_AUTH /public/tags/1/documents | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Tag not found"}
  Response: {"error":"Not Found","message":"Tag not found"}

[23:40:26] document_files | GET /documents/1/files | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[23:40:26] document_files | GET /documents/1/metadata | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[23:40:27] document_analysis | GET /documents/1/metadata | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[23:40:27] document_analysis | GET /documents/1/classification | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[23:40:27] document_analysis | GET /documents/1/entities | Status: 404 | Type: NOT_FOUND_ERROR | Critical: false
  Error: HTTP 404 error: {"error":"Not Found","message":"Document not found"}
  Response: {"error":"Not Found","message":"Document not found"}

[23:40:27] auto_generation | POST /auto-generation/generate | Status: 500 | Type: SERVER_ERROR | Critical: true
  Error: HTTP 500 error: {"error":"Internal Server Error","message":"Failed to create default auto-generation config: ERROR: column \"user_id\" of relation \"auto_generation_configs\" does not exist (SQLSTATE 42703)"}
  Response: {"error":"Internal Server Error","message":"Failed to create default auto-generation config: ERROR: column \"user_id\" of relation \"auto_generation_configs\" does not exist (SQLSTATE 42703)"}

[23:40:28] regulation_chunks | CREATE /regulations/1/chunks | Status: 403 | Type: PERMISSION_ERROR | Critical: false
  Error: HTTP 403 error: {"error":"Insufficient permissions"}
  Response: {"error":"Insufficient permissions"}

