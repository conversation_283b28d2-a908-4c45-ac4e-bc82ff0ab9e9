{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMIe7T6kIbqWID+9XSG6Sx67cuIQ4yC8v4gkCW7mjhA=", "__NEXT_PREVIEW_MODE_ID": "2c6d6729717a5cf538247d74b84c0cef", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6d6dcb31a0fbdd2574fe233dc8b6ae0c47ba1e45fe7bc605a7bfa705f982a996", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "736db4153bba983c13656a6839648b41c24f11ee3b44d6e0cf6244bc50d7b5a9"}}}, "functions": {}, "sortedMiddleware": ["/"]}